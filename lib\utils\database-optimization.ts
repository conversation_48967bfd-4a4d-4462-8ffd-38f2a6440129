import { prisma } from '@/lib/config/prisma';
import { dbCacheUtils, cacheKeys } from './performance-cache';

// Database connection pool optimization
export const dbOptimization = {
  // Optimized product queries
  getProducts: async (filters: any = {}) => {
    const cacheKey = cacheKeys.products(filters);
    
    // Try cache first
    const cached = dbCacheUtils.get(cacheKey);
    if (cached) return cached;
    
    const {
      search,
      category,
      capacity,
      minPrice = 0,
      maxPrice = 1000000000,
      availability,
      page = 1,
      limit = 9
    } = filters;
    
    const offset = (page - 1) * limit;
    
    // Build optimized where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (category) where.category = category;
    if (capacity) where.capacity = parseInt(capacity);
    if (availability) where.status = availability;
    
    if (minPrice > 0 || maxPrice < 1000000000) {
      where.price = {};
      if (minPrice > 0) where.price.gte = minPrice;
      if (maxPrice < 1000000000) where.price.lte = maxPrice;
    }
    
    // Use Promise.all for parallel queries
    const [products, totalProducts] = await Promise.all([
      prisma.product.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          overtimeRate: true,
          capacity: true,
          category: true,
          status: true,
          imageUrl: true,
          createdAt: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      }),
      prisma.product.count({ where })
    ]);
    
    const result = {
      items: products,
      total: totalProducts,
      page,
      limit,
      totalPages: Math.ceil(totalProducts / limit),
    };
    
    // Cache for 10 minutes
    dbCacheUtils.set(cacheKey, result, 1000 * 60 * 10);
    
    return result;
  },

  // Optimized user profile query
  getUserProfile: async (userId: string) => {
    const cacheKey = cacheKeys.user(userId);
    
    const cached = dbCacheUtils.get(cacheKey);
    if (cached) return cached;
    
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        whatsappNumber: true,
        createdAt: true,
        _count: {
          select: {
            rentals: true,
            products: true,
          }
        }
      }
    });
    
    // Cache for 5 minutes
    dbCacheUtils.set(cacheKey, user, 1000 * 60 * 5);
    
    return user;
  },

  // Optimized rentals query
  getUserRentals: async (userId: string, filters: any = {}) => {
    const cacheKey = cacheKeys.rentals(userId, filters);
    
    const cached = dbCacheUtils.get(cacheKey);
    if (cached) return cached;
    
    const { status, page = 1, limit = 10 } = filters;
    const offset = (page - 1) * limit;
    
    const where: any = { userId };
    if (status) where.status = status;
    
    const [rentals, totalRentals] = await Promise.all([
      prisma.rental.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          startDate: true,
          endDate: true,
          totalPrice: true,
          status: true,
          createdAt: true,
          product: {
            select: {
              id: true,
              name: true,
              imageUrl: true,
              price: true,
              overtimeRate: true,
            }
          },
          payments: {
            select: {
              id: true,
              amount: true,
              status: true,
              type: true,
              createdAt: true,
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      }),
      prisma.rental.count({ where })
    ]);
    
    const result = {
      items: rentals,
      total: totalRentals,
      page,
      limit,
      totalPages: Math.ceil(totalRentals / limit),
    };
    
    // Cache for 3 minutes
    dbCacheUtils.set(cacheKey, result, 1000 * 60 * 3);
    
    return result;
  },

  // Optimized dashboard stats
  getDashboardStats: async (userId: string) => {
    const cacheKey = cacheKeys.dashboard(userId);
    
    const cached = dbCacheUtils.get(cacheKey);
    if (cached) return cached;
    
    const [
      totalRentals,
      activeRentals,
      completedRentals,
      totalSpent,
      recentRentals
    ] = await Promise.all([
      prisma.rental.count({ where: { userId } }),
      prisma.rental.count({ where: { userId, status: 'ACTIVE' } }),
      prisma.rental.count({ where: { userId, status: 'COMPLETED' } }),
      prisma.rental.aggregate({
        where: { userId, status: 'COMPLETED' },
        _sum: { totalPrice: true }
      }),
      prisma.rental.findMany({
        where: { userId },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          startDate: true,
          endDate: true,
          totalPrice: true,
          status: true,
          product: {
            select: {
              name: true,
              imageUrl: true,
            }
          }
        }
      })
    ]);
    
    const stats = {
      totalRentals,
      activeRentals,
      completedRentals,
      totalSpent: totalSpent._sum.totalPrice || 0,
      recentRentals,
    };
    
    // Cache for 2 minutes
    dbCacheUtils.set(cacheKey, stats, 1000 * 60 * 2);
    
    return stats;
  },

  // Optimized admin stats
  getAdminStats: async () => {
    const cacheKey = cacheKeys.adminStats();
    
    const cached = dbCacheUtils.get(cacheKey);
    if (cached) return cached;
    
    const [
      totalUsers,
      totalProducts,
      totalRentals,
      totalRevenue,
      activeRentals,
      pendingPayments
    ] = await Promise.all([
      prisma.user.count({ where: { role: 'USER' } }),
      prisma.product.count(),
      prisma.rental.count(),
      prisma.payment.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { amount: true }
      }),
      prisma.rental.count({ where: { status: 'ACTIVE' } }),
      prisma.payment.count({ where: { status: 'PENDING' } })
    ]);
    
    const stats = {
      totalUsers,
      totalProducts,
      totalRentals,
      totalRevenue: totalRevenue._sum.amount || 0,
      activeRentals,
      pendingPayments,
    };
    
    // Cache for 5 minutes
    dbCacheUtils.set(cacheKey, stats, 1000 * 60 * 5);
    
    return stats;
  },
};

// Database connection monitoring
export const dbMonitoring = {
  // Check database health
  checkHealth: async () => {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date() };
    }
  },

  // Get connection info
  getConnectionInfo: async () => {
    try {
      const result = await prisma.$queryRaw`
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections,
          count(*) FILTER (WHERE state = 'idle') as idle_connections
        FROM pg_stat_activity 
        WHERE datname = current_database()
      `;
      return result;
    } catch (error) {
      console.error('Failed to get connection info:', error);
      return null;
    }
  },

  // Get slow queries
  getSlowQueries: async () => {
    try {
      const result = await prisma.$queryRaw`
        SELECT query, mean_exec_time, calls, total_exec_time
        FROM pg_stat_statements 
        WHERE mean_exec_time > 100
        ORDER BY mean_exec_time DESC 
        LIMIT 10
      `;
      return result;
    } catch (error) {
      console.error('Failed to get slow queries:', error);
      return [];
    }
  },
};

// Cache invalidation helpers
export const cacheInvalidation = {
  invalidateUserData: (userId: string) => {
    dbCacheUtils.invalidatePattern(`user:${userId}`);
    dbCacheUtils.invalidatePattern(`rentals:${userId}`);
    dbCacheUtils.invalidatePattern(`dashboard:${userId}`);
  },

  invalidateProductData: () => {
    dbCacheUtils.invalidatePattern('products');
    dbCacheUtils.invalidatePattern('product:');
  },

  invalidateAdminData: () => {
    dbCacheUtils.invalidatePattern('admin');
    dbCacheUtils.invalidatePattern('operations');
  },
};
