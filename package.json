{"name": "rental-ganset", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--max-old-space-size=8192 && next dev", "build": "set NODE_OPTIONS=--max-old-space-size=8192 && next build", "start": "set NODE_OPTIONS=--max-old-space-size=8192 && next start", "lint": "next lint", "update-overtime-rates": "npx tsx scripts/update-overtime-rates.ts", "migrate-auth": "npx tsx scripts/migrate-to-better-auth.ts", "test-auth": "npx tsx scripts/test-better-auth.ts", "perf:test": "node scripts/performance-test.js", "perf:build": "npm run build && npm run perf:test", "analyze": "cross-env ANALYZE=true npm run build", "images:convert": "node scripts/convert-to-webp.js", "images:generate": "node scripts/generate-placeholder-images.js", "images:icons": "node scripts/generate-pwa-icons.js", "images:logos": "node scripts/generate-logo.js", "images:optimize": "npm run images:generate && npm run images:icons && npm run images:logos && npm run images:convert"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.4.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@sparticuz/chromium": "^133.0.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.8", "@tailwindcss/typography": "^0.5.16", "@types/html2canvas": "^0.5.35", "@types/leaflet": "^1.9.17", "@types/lodash": "^4.17.16", "@vercel/blob": "^0.27.3", "bcrypt-ts": "^5.0.3", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^12.11.0", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "kysely": "^0.28.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lru-cache": "^11.0.2", "lucide-react": "^0.475.0", "maplibre-gl": "^5.5.0", "midtrans-client": "^1.4.2", "next": "^15.2.4", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "puppeteer": "^24.8.2", "puppeteer-core": "^24.8.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-map-gl": "^8.0.4", "recharts": "^2.15.1", "rental-ganset": "file:", "resend": "^4.5.1", "sharp": "^0.34.3", "sonner": "^1.7.4", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "use-debounce": "^10.0.4", "use-sound": "^4.0.3", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8.5.3", "prisma": "^6.4.1", "tailwindcss": "^4.0.8", "typescript": "^5"}}