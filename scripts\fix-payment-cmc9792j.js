// <PERSON>ript to fix payment status for CMC9792J
// Run this in browser console while logged in as admin

async function fixPaymentCMC9792J() {
  try {
    console.log('🔍 Checking payment status for CMC9792J...');
    
    // First, check current status
    const checkResponse = await fetch('/api/payments/fix-status?code=CMC9792J');
    const checkData = await checkResponse.json();
    
    if (!checkData.success) {
      console.error('❌ Payment not found:', checkData.error);
      return;
    }
    
    console.log('📋 Current payment info:', checkData.payment);
    console.log('📊 Current status:', checkData.payment.status);
    
    if (checkData.payment.status === 'DEPOSIT_PENDING') {
      console.log('🔧 Fixing payment status...');
      
      // Fix the payment status
      const fixResponse = await fetch('/api/payments/fix-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentCode: 'CMC9792J' }),
      });
      
      const fixData = await fixResponse.json();
      
      if (fixData.success) {
        console.log('✅ Payment status fixed successfully!');
        console.log('🔄 Please refresh the payments page to see the changes.');
        
        // Optionally reload the page
        if (confirm('Payment status has been fixed! Would you like to reload the page to see the changes?')) {
          window.location.reload();
        }
      } else {
        console.error('❌ Failed to fix payment status:', fixData.message);
      }
    } else {
      console.log('ℹ️ Payment status is already:', checkData.payment.status);
      console.log('✅ No fix needed!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the fix
fixPaymentCMC9792J();
