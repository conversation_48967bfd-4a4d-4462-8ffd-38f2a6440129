import { Payment } from '@prisma/client';
import { db } from '../db';

type PaymentStatus = 'DEPOSIT_PENDING' | 'DEPOSIT_PAID' | 'FULLY_PAID' | 'FAILED' | 'INVOICE_ISSUED';

export class PaymentRepository {
  async findById(id: string): Promise<Payment | null> {
    return db.payment.findUnique({
      where: { id }
    });
  }

  async findByRentalId(rentalId: string): Promise<Payment | null> {
    return db.payment.findUnique({
      where: { rentalId }
    });
  }

  async findAll(options?: {
    status?: PaymentStatus,
    userId?: string,
    limit?: number,
    offset?: number,
    include?: { rental?: boolean, user?: boolean }
  }): Promise<Payment[]> {
    return db.payment.findMany({
      where: {
        status: options?.status,
        rental: options?.userId ? {
          userId: options.userId
        } : undefined
      },
      take: options?.limit,
      skip: options?.offset,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        rental: options?.include?.rental ? {
          include: {
            user: options?.include?.user || false
          }
        } : false
      }
    });
  }

  async create(data: Omit<Payment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Payment> {
    return db.payment.create({
      data: {
        ...data,
        status: data.status || 'DEPOSIT_PENDING'
      }
    });
  }

  async update(id: string, data: Partial<Omit<Payment, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Payment> {
    return db.payment.update({
      where: { id },
      data
    });
  }

  async updateStatus(id: string, status: PaymentStatus, userId: string): Promise<Payment> {
    // Start a transaction to update status and create history
    return db.$transaction(async (tx) => {
      // Update payment status
      const payment = await tx.payment.update({
        where: { id },
        data: { status }
      });

      // Create payment status history
      await tx.paymentStatusHistory.create({
        data: {
          paymentId: id,
          userId,
          newStatus: status
        }
      });

      return payment;
    });
  }

  async getPaymentHistory(paymentId: string) {
    return db.paymentStatusHistory.findMany({
      where: { paymentId },
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });
  }

  async getTotalRevenue(): Promise<number> {
    const result = await db.payment.aggregate({
      _sum: {
        amount: true
      },
      where: {
        status: {
          in: ['FULLY_PAID', 'DEPOSIT_PAID']
        }
      }
    });

    return result._sum.amount || 0;
  }

  async getMonthlyRevenue(year: number): Promise<Array<{ month: number, revenue: number }>> {
    const payments = await db.payment.findMany({
      where: {
        createdAt: {
          gte: new Date(`${year}-01-01`),
          lt: new Date(`${year + 1}-01-01`),
        },
        status: {
          in: ['FULLY_PAID', 'DEPOSIT_PAID']
        }
      },
      select: {
        amount: true,
        createdAt: true
      }
    });

    const monthlyRevenue = Array(12).fill(0).map((_, i) => ({
      month: i + 1,
      revenue: 0
    }));

    payments.forEach(payment => {
      const month = payment.createdAt.getMonth();
      monthlyRevenue[month].revenue += payment.amount;
    });

    return monthlyRevenue;
  }
} 
