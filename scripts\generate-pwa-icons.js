// Script untuk generate PWA icons yang diperlukan
const fs = require('fs');
const path = require('path');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.log('❌ Sharp not found. Run: npm install sharp');
  process.exit(1);
}

const imagesDir = path.join(__dirname, '../public/images');

// PWA icon sizes yang diperlukan
const iconSizes = [
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' },
];

// Generate base icon SVG
function createBaseIconSVG(size) {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
    <!-- Background circle -->
    <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="#8B5CF6"/>
    
    <!-- Generator icon -->
    <g transform="translate(${size*0.2}, ${size*0.2}) scale(${size*0.006})">
      <!-- Generator body -->
      <rect x="10" y="30" width="80" height="50" rx="5" fill="white" opacity="0.9"/>
      
      <!-- Control panel -->
      <rect x="15" y="35" width="25" height="15" rx="2" fill="#8B5CF6" opacity="0.8"/>
      
      <!-- Exhaust -->
      <rect x="75" y="25" width="8" height="20" rx="4" fill="white" opacity="0.7"/>
      
      <!-- Handle -->
      <rect x="85" y="45" width="10" height="4" rx="2" fill="white" opacity="0.8"/>
      
      <!-- Power symbol -->
      <circle cx="27.5" cy="42.5" r="3" fill="white"/>
      <path d="M27.5 40 L27.5 45 M25 42.5 L30 42.5" stroke="#8B5CF6" stroke-width="1" stroke-linecap="round"/>
    </g>
    
    <!-- Text label for larger icons -->
    ${size >= 144 ? `<text x="${size/2}" y="${size*0.85}" font-family="Arial, sans-serif" font-size="${size*0.08}" font-weight="bold" text-anchor="middle" fill="white">GENSET</text>` : ''}
  </svg>`;
}

// Generate favicon ICO
function createFaviconSVG() {
  return `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="4" fill="#8B5CF6"/>
    <g transform="translate(6, 8) scale(0.2)">
      <rect x="10" y="30" width="80" height="50" rx="5" fill="white"/>
      <rect x="15" y="35" width="25" height="15" rx="2" fill="#8B5CF6"/>
      <rect x="75" y="25" width="8" height="20" rx="4" fill="white" opacity="0.7"/>
      <circle cx="27.5" cy="42.5" r="3" fill="white"/>
    </g>
  </svg>`;
}

async function generatePWAIcons() {
  console.log('🎨 Generating PWA icons...');
  
  // Create images directory if it doesn't exist
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  let generated = 0;

  // Generate PWA icons
  for (const icon of iconSizes) {
    const outputPath = path.join(imagesDir, icon.name);
    
    if (fs.existsSync(outputPath)) {
      console.log(`⏭️  Skipping ${icon.name} (already exists)`);
      continue;
    }

    try {
      const svg = createBaseIconSVG(icon.size);
      
      await sharp(Buffer.from(svg))
        .png({ quality: 90 })
        .toFile(outputPath);
      
      console.log(`✅ Generated ${icon.name} (${icon.size}x${icon.size})`);
      generated++;
    } catch (error) {
      console.error(`❌ Error generating ${icon.name}:`, error.message);
    }
  }

  // Generate favicon.ico
  const faviconPath = path.join(__dirname, '../public/favicon.ico');
  if (!fs.existsSync(faviconPath)) {
    try {
      const faviconSvg = createFaviconSVG();
      
      await sharp(Buffer.from(faviconSvg))
        .resize(32, 32)
        .png()
        .toFile(faviconPath.replace('.ico', '.png'));
      
      // Also create as ICO (using PNG for simplicity)
      await sharp(Buffer.from(faviconSvg))
        .resize(32, 32)
        .png()
        .toFile(faviconPath);
      
      console.log('✅ Generated favicon.ico');
      generated++;
    } catch (error) {
      console.error('❌ Error generating favicon:', error.message);
    }
  }

  // Generate apple-touch-icon
  const appleTouchIconPath = path.join(__dirname, '../public/apple-touch-icon.png');
  if (!fs.existsSync(appleTouchIconPath)) {
    try {
      const appleSvg = createBaseIconSVG(180);
      
      await sharp(Buffer.from(appleSvg))
        .png({ quality: 90 })
        .toFile(appleTouchIconPath);
      
      console.log('✅ Generated apple-touch-icon.png');
      generated++;
    } catch (error) {
      console.error('❌ Error generating apple-touch-icon:', error.message);
    }
  }

  // Generate shortcut icons for manifest
  const shortcutIcons = [
    { name: 'shortcut-rent.png', text: 'SEWA', color: '#10B981' },
    { name: 'shortcut-history.png', text: 'HISTORY', color: '#F59E0B' }
  ];

  for (const shortcut of shortcutIcons) {
    const outputPath = path.join(imagesDir, shortcut.name);
    
    if (fs.existsSync(outputPath)) {
      console.log(`⏭️  Skipping ${shortcut.name} (already exists)`);
      continue;
    }

    try {
      const svg = `<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
        <rect width="96" height="96" rx="12" fill="${shortcut.color}"/>
        <text x="48" y="58" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">${shortcut.text}</text>
      </svg>`;
      
      await sharp(Buffer.from(svg))
        .png({ quality: 90 })
        .toFile(outputPath);
      
      console.log(`✅ Generated ${shortcut.name}`);
      generated++;
    } catch (error) {
      console.error(`❌ Error generating ${shortcut.name}:`, error.message);
    }
  }

  console.log(`\n🎉 PWA icon generation completed!`);
  console.log(`📊 Generated: ${generated} new icons`);
  console.log(`📁 Location: public/images/ and public/`);
}

// Run the script
if (require.main === module) {
  generatePWAIcons().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { generatePWAIcons };
