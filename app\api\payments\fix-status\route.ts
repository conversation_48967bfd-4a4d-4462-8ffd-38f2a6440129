import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";

export async function POST(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { paymentCode } = await request.json();

    if (!paymentCode) {
      return NextResponse.json({ error: "Payment code is required" }, { status: 400 });
    }

    // Find payment by ID (using the payment code from the screenshot)
    // The payment code appears to be the first 8 characters of the payment ID
    const payments = await prisma.payment.findMany({
      where: {
        id: {
          startsWith: paymentCode.replace('#', '').toLowerCase()
        }
      },
      include: {
        rental: {
          include: {
            user: true,
            product: true
          }
        }
      }
    });

    if (payments.length === 0) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    const payment = payments[0];

    // Check if user owns this payment (or is admin)
    if (session.user.role !== "ADMIN" && payment.rental.userId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Update payment status to DEPOSIT_PAID if it's currently DEPOSIT_PENDING
    if (payment.status === "DEPOSIT_PENDING") {
      const updatedPayment = await prisma.$transaction(async (tx) => {
        // Update payment status
        const updated = await tx.payment.update({
          where: { id: payment.id },
          data: {
            status: "DEPOSIT_PAID",
            transactionId: `MANUAL_FIX_${Date.now()}`,
            updatedAt: new Date()
          }
        });

        // Update rental status to ACTIVE
        await tx.rental.update({
          where: { id: payment.rentalId },
          data: {
            status: "ACTIVE"
          }
        });

        return updated;
      });

      return NextResponse.json({
        success: true,
        message: "Payment status updated successfully",
        payment: {
          id: updatedPayment.id,
          status: updatedPayment.status,
          previousStatus: payment.status
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `Payment status is already ${payment.status}`,
        currentStatus: payment.status
      });
    }

  } catch (error) {
    console.error("[FIX_PAYMENT_STATUS]", error);
    return NextResponse.json(
      { error: "Failed to fix payment status" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const paymentCode = url.searchParams.get('code');

    if (!paymentCode) {
      return NextResponse.json({ error: "Payment code is required" }, { status: 400 });
    }

    // Find payment by ID prefix
    const payments = await prisma.payment.findMany({
      where: {
        id: {
          startsWith: paymentCode.replace('#', '').toLowerCase()
        }
      },
      include: {
        rental: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (payments.length === 0) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    const payment = payments[0];

    // Check if user owns this payment (or is admin)
    if (session.user.role !== "ADMIN" && payment.rental.userId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    return NextResponse.json({
      success: true,
      payment: {
        id: payment.id,
        status: payment.status,
        amount: payment.amount,
        deposit: payment.deposit,
        remaining: payment.remaining,
        transactionId: payment.transactionId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        rental: {
          id: payment.rental.id,
          status: payment.rental.status,
          user: payment.rental.user,
          product: payment.rental.product
        }
      }
    });

  } catch (error) {
    console.error("[GET_PAYMENT_STATUS]", error);
    return NextResponse.json(
      { error: "Failed to get payment status" },
      { status: 500 }
    );
  }
}
