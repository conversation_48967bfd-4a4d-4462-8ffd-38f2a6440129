// Process Callback for CMC9792J Payment
// Based on the console log data we saw

async function processCallbackCMC9792J() {
  console.log('🔄 PROCESSING CALLBACK FOR CMC9792J');
  console.log('==================================');
  
  // Data from the console log
  const callbackData = {
    orderId: 'cmc9792jl0001tm8wx6mv36gy_deposit_1752241747768',
    transactionStatus: 'settlement',
    transactionId: '073fa895-52ff-4483-bf2f-f813d53969b1',
    statusCode: '200',
    statusMessage: 'Success, transaction is found',
    paymentType: 'qris',
    grossAmount: '1000000.00'
  };
  
  console.log('📋 Callback Data:', callbackData);
  
  try {
    console.log('🔄 Step 1: Processing manual callback...');
    
    const response = await fetch('/api/payments/manual-callback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId: callbackData.orderId,
        transactionStatus: callbackData.transactionStatus,
        transactionId: callbackData.transactionId
      }),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ CALLBACK PROCESSED SUCCESSFULLY!');
      console.log('📊 Result:', result);
      console.log('');
      console.log('🎉 Payment Status Changes:');
      console.log('  Rental ID:', result.data.rentalId);
      console.log('  Payment Type:', result.data.paymentType);
      console.log('  Payment Status:', result.data.oldPaymentStatus, '->', result.data.newPaymentStatus);
      console.log('  Rental Status:', result.data.rentalStatus);
      console.log('  Transaction ID:', result.data.transactionId);
      
      alert('✅ Payment callback processed successfully! The page will reload to show the updated status.');
      
      // Force reload to see changes
      window.location.reload();
      
    } else {
      console.error('❌ CALLBACK PROCESSING FAILED:', result.message);
      console.error('Details:', result);
    }
    
  } catch (error) {
    console.error('❌ ERROR processing callback:', error);
  }
  
  console.log('==================================');
  console.log('🏁 Callback processing completed');
}

// Helper function to simulate the original callback URL
function simulateCallbackURL() {
  const callbackURL = 'http://localhost:3000/user/payments/callback?order_id=cmc9792jl0001tm8wx6mv36gy_deposit_1752241747768&status=success&status_code=200&transaction_status=settlement';
  
  console.log('🔗 Original callback URL:', callbackURL);
  
  if (confirm('Do you want to navigate to the original callback URL to see if it processes correctly?')) {
    window.location.href = callbackURL;
  }
}

// Helper function to check current payment status
async function checkCurrentStatus() {
  console.log('🔍 Checking current payment status...');
  
  try {
    const response = await fetch('/api/payments/inspect');
    const data = await response.json();
    
    if (data.success) {
      const targetPayment = data.targetPaymentPrisma;
      
      if (targetPayment) {
        console.log('📊 Current Payment Status:');
        console.log('  Payment ID:', targetPayment.id);
        console.log('  Status:', targetPayment.status);
        console.log('  Transaction ID:', targetPayment.transactionId);
        console.log('  Rental Status:', targetPayment.rental.status);
        console.log('  Expected Progress:', targetPayment.status === 'DEPOSIT_PAID' ? '50%' : '0%');
      } else {
        console.log('❌ Payment not found');
      }
    } else {
      console.error('❌ Failed to check status:', data.error);
    }
  } catch (error) {
    console.error('❌ Error checking status:', error);
  }
}

console.log('🚀 CALLBACK PROCESSING TOOLS LOADED');
console.log('Available functions:');
console.log('- processCallbackCMC9792J() - Process the callback manually');
console.log('- simulateCallbackURL() - Navigate to original callback URL');
console.log('- checkCurrentStatus() - Check current payment status');
console.log('');
console.log('🎯 Running callback processing...');

// Auto-run the callback processing
processCallbackCMC9792J();
