import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";

export async function GET(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || session.user.id;

    // Get all payments for the user with detailed information
    const payments = await prisma.payment.findMany({
      where: {
        rental: {
          userId: userId
        }
      },
      include: {
        rental: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get detailed info for each payment
    const detailedPayments = payments.map(payment => {
      const paymentCode = payment.id.substring(0, 8).toUpperCase();
      
      return {
        paymentCode: `#${paymentCode}`,
        id: payment.id,
        rentalId: payment.rentalId,
        status: payment.status,
        amount: payment.amount,
        deposit: payment.deposit,
        remaining: payment.remaining,
        overtimeCost: payment.overtimeCost,
        transactionId: payment.transactionId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        rental: {
          id: payment.rental.id,
          status: payment.rental.status,
          amount: payment.rental.amount,
          user: payment.rental.user,
          product: payment.rental.product
        },
        // Calculate what the UI should show
        uiCalculations: {
          progressPercentage: payment.status === "FULLY_PAID" ? 100 : payment.status === "DEPOSIT_PAID" ? 50 : 0,
          shouldShowLunasiButton: payment.status === "DEPOSIT_PAID",
          statusBadge: getStatusBadgeInfo(payment.status),
          isCompleted: payment.rental.operationalEnd != null
        }
      };
    });

    return NextResponse.json({
      success: true,
      userId: userId,
      sessionUser: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role
      },
      totalPayments: payments.length,
      payments: detailedPayments
    });

  } catch (error) {
    console.error("[DETAILED_DEBUG]", error);
    return NextResponse.json(
      { error: "Failed to get detailed payment info", details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { action, paymentId, newStatus } = await request.json();

    if (action === "force_update" && paymentId && newStatus) {
      console.log(`[FORCE_UPDATE] Updating payment ${paymentId} to status ${newStatus}`);
      
      // Force update payment status
      const payment = await prisma.$transaction(async (tx) => {
        // Update payment
        const updatedPayment = await tx.payment.update({
          where: { id: paymentId },
          data: {
            status: newStatus,
            transactionId: `FORCE_UPDATE_${Date.now()}`,
            updatedAt: new Date()
          }
        });

        // Update rental status based on payment status
        let rentalStatus = "PENDING";
        if (newStatus === "DEPOSIT_PAID") {
          rentalStatus = "ACTIVE";
        } else if (newStatus === "FULLY_PAID") {
          rentalStatus = "COMPLETED";
        }

        await tx.rental.update({
          where: { id: updatedPayment.rentalId },
          data: {
            status: rentalStatus
          }
        });

        return updatedPayment;
      });

      // Clear any caches
      try {
        // Force revalidation of the payments page
        const { revalidatePath } = await import("next/cache");
        revalidatePath("/user/payments");
        revalidatePath("/admin/payments");
      } catch (revalidateError) {
        console.warn("Could not revalidate paths:", revalidateError);
      }

      return NextResponse.json({
        success: true,
        message: `Payment ${paymentId} updated to ${newStatus}`,
        payment: {
          id: payment.id,
          status: payment.status,
          transactionId: payment.transactionId,
          updatedAt: payment.updatedAt
        }
      });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });

  } catch (error) {
    console.error("[FORCE_UPDATE_ERROR]", error);
    return NextResponse.json(
      { error: "Failed to force update payment", details: error.message },
      { status: 500 }
    );
  }
}

function getStatusBadgeInfo(status: string) {
  switch (status.toLowerCase()) {
    case "fully_paid":
      return { label: "Lunas", variant: "default", color: "green" };
    case "deposit_paid":
      return { label: "Deposit Dibayar", variant: "success", color: "blue" };
    case "deposit_pending":
      return { label: "Menunggu Deposit", variant: "secondary", color: "yellow" };
    case "failed":
      return { label: "Gagal", variant: "destructive", color: "red" };
    default:
      return { label: "Menunggu", variant: "outline", color: "gray" };
  }
}
