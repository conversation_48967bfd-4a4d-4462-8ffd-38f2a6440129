# DAFTAR GAMBAR

**Gambar 3.1** Model SDLC Waterfall ................................................................. 13

**Gambar 3.2** Tahapan Pengembangan Sistem ......................................................... 54

**Gambar 3.3** Entity-Relationship Diagram ........................................................... 519

**Gambar 3.4** Context Diagram ........................................................................... 590

**Gambar 3.5** Data Flow Diagram Level 1 .............................................................. 614

**Gambar 3.6** Bagan Berjenjang Sistem ................................................................. 747

**Gambar 3.7** Site Map Sistem ............................................................................ 833

**Gambar 3.8** Layout Dashboard Admin ................................................................. 935

**Gambar 3.9** Layout Dashboard User ................................................................... 977

**Gambar 3.10** Desain Laporan Transaksi ............................................................. 1021

**Gambar 3.11** Desain Invoice ............................................................................ 1064

**Gambar 4.1** Use Case Diagram Sistem Rental Genset .......................................... 38

**Gambar 4.2** Entity-Relationship Diagram (ERD) Sistem Rental Genset ................. 41

**Gambar 4.3** Data Flow Diagram Level 0 (Context Diagram) .................................. 44

**Gambar 4.4** Data Flow Diagram Level 1 .............................................................. 45

**Gambar 4.5** Sequence Diagram - Proses Pemesanan .......................................... 47

**Gambar 4.6** Sequence Diagram - Kelola Produk Admin ....................................... 48

**Gambar 4.7** Arsitektur Sistem Informasi Rental Genset ....................................... 49

**Gambar 4.8** Tampilan Halaman Beranda (Homepage) ......................................... 51

**Gambar 4.9** Tampilan Hero Section dengan Call-to-Action .................................. 51

**Gambar 4.10** Tampilan Katalog Produk di Homepage ......................................... 52

**Gambar 4.11** Tampilan Halaman Login ............................................................... 53

**Gambar 4.12** Tampilan Halaman Registrasi ........................................................ 53

**Gambar 4.13** Tampilan Form Login dengan Validasi ........................................... 54

**Gambar 4.14** Tampilan Halaman Katalog Produk ............................................... 55

**Gambar 4.15** Tampilan Filter dan Pencarian Produk .......................................... 55

**Gambar 4.16** Tampilan Card Produk dengan Informasi Detail ............................. 56

**Gambar 4.17** Tampilan Halaman Detail Produk .................................................. 57

**Gambar 4.18** Tampilan Form Pemesanan dengan Date Picker ............................. 58

**Gambar 4.19** Tampilan Kalkulasi Biaya Real-time .............................................. 58

**Gambar 4.20** Tampilan Dashboard Pengguna ..................................................... 60

**Gambar 4.21** Tampilan Stats Card di Dashboard Pengguna ................................ 60

**Gambar 4.22** Tampilan Pesanan Aktif di Dashboard Pengguna ........................... 61

**Gambar 4.23** Tampilan Riwayat Pembayaran ..................................................... 61

**Gambar 4.24** Tampilan Dashboard Admin .......................................................... 63

**Gambar 4.25** Tampilan KPI Cards di Dashboard Admin ...................................... 63

**Gambar 4.26** Tampilan Recent Orders di Dashboard Admin ............................... 64

**Gambar 4.27** Tampilan Revenue Chart ............................................................... 64

**Gambar 4.28** Tampilan Quick Actions Menu ....................................................... 65

**Gambar 4.29** Alur Autentikasi dengan Better Auth ............................................. 67

**Gambar 4.30** Tampilan Middleware Authentication Flow .................................... 68

**Gambar 4.31** Tampilan Halaman Manajemen Produk Admin .............................. 72

**Gambar 4.32** Tampilan Form Tambah Produk .................................................... 73

**Gambar 4.33** Tampilan Form Edit Produk .......................................................... 73

**Gambar 4.34** Tampilan Konfirmasi Hapus Produk .............................................. 74

**Gambar 4.35** Alur Integrasi Payment Gateway Midtrans .................................... 77

**Gambar 4.36** Tampilan Halaman Pembayaran Midtrans ..................................... 78

**Gambar 4.37** Tampilan Payment Success Page .................................................. 78

**Gambar 4.38** Tampilan Invoice PDF ................................................................... 79

**Gambar 4.39** Contoh Notifikasi WhatsApp untuk Pesanan Baru ......................... 82

**Gambar 4.40** Contoh Notifikasi WhatsApp untuk Konfirmasi Pembayaran .......... 82

**Gambar 4.41** Tampilan Log Notifikasi di Dashboard Admin ................................ 83

**Gambar 4.42** Tampilan Halaman Laporan dan Analitik ....................................... 87

**Gambar 4.43** Tampilan Grafik Pendapatan Bulanan ........................................... 87

**Gambar 4.44** Tampilan Laporan Transaksi dengan Filter .................................... 88

**Gambar 4.45** Tampilan Export Laporan ke PDF ................................................. 88

**Gambar 4.46** Hasil Pengujian Black Box Testing ............................................... 92

**Gambar 4.47** Hasil Pengujian Integrasi Payment Gateway ................................. 96

**Gambar 4.48** Hasil Pengujian Integrasi WhatsApp API ....................................... 97

**Gambar 4.49** Hasil Pengujian Performance dengan Lighthouse .......................... 99

**Gambar 4.50** Hasil Pengujian Load Testing ....................................................... 99

**Gambar 4.51** Hasil Pengujian Security Scan ..................................................... 101
