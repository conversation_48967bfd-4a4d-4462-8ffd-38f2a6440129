// Script untuk konversi gambar ke format WebP
const fs = require('fs');
const path = require('path');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.log('❌ Sharp not found. Installing...');
  console.log('Run: npm install sharp');
  process.exit(1);
}

const imagesDir = path.join(__dirname, '../public/images');
const supportedFormats = ['.jpg', '.jpeg', '.png', '.tiff', '.gif'];

async function convertToWebP(inputPath, outputPath, quality = 85) {
  try {
    await sharp(inputPath)
      .webp({ quality })
      .toFile(outputPath);
    
    const inputStats = fs.statSync(inputPath);
    const outputStats = fs.statSync(outputPath);
    const savings = ((inputStats.size - outputStats.size) / inputStats.size * 100).toFixed(1);
    
    console.log(`✅ ${path.basename(inputPath)} → ${path.basename(outputPath)}`);
    console.log(`   Size: ${(inputStats.size / 1024).toFixed(1)}KB → ${(outputStats.size / 1024).toFixed(1)}KB (${savings}% smaller)`);
    
    return true;
  } catch (error) {
    console.error(`❌ Error converting ${inputPath}:`, error.message);
    return false;
  }
}

async function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`📁 Creating images directory: ${dirPath}`);
    fs.mkdirSync(dirPath, { recursive: true });
    return;
  }

  const files = fs.readdirSync(dirPath);
  const imageFiles = files.filter(file => {
    const ext = path.extname(file).toLowerCase();
    return supportedFormats.includes(ext);
  });

  if (imageFiles.length === 0) {
    console.log('📷 No images found to convert');
    return;
  }

  console.log(`🔄 Found ${imageFiles.length} images to convert:`);
  
  let converted = 0;
  let totalOriginalSize = 0;
  let totalWebPSize = 0;

  for (const file of imageFiles) {
    const inputPath = path.join(dirPath, file);
    const nameWithoutExt = path.parse(file).name;
    const outputPath = path.join(dirPath, `${nameWithoutExt}.webp`);
    
    // Skip if WebP version already exists
    if (fs.existsSync(outputPath)) {
      console.log(`⏭️  Skipping ${file} (WebP version exists)`);
      continue;
    }

    const success = await convertToWebP(inputPath, outputPath);
    if (success) {
      converted++;
      
      const originalStats = fs.statSync(inputPath);
      const webpStats = fs.statSync(outputPath);
      totalOriginalSize += originalStats.size;
      totalWebPSize += webpStats.size;
    }
  }

  if (converted > 0) {
    const totalSavings = ((totalOriginalSize - totalWebPSize) / totalOriginalSize * 100).toFixed(1);
    console.log(`\n📊 Conversion Summary:`);
    console.log(`   Converted: ${converted} images`);
    console.log(`   Original size: ${(totalOriginalSize / 1024).toFixed(1)}KB`);
    console.log(`   WebP size: ${(totalWebPSize / 1024).toFixed(1)}KB`);
    console.log(`   Total savings: ${totalSavings}%`);
  }
}

// Generate sample images for testing
async function generateSampleImages() {
  console.log('🎨 Generating sample images for testing...');
  
  const samples = [
    {
      name: 'testimonial-1.webp',
      width: 96,
      height: 96,
      color: '#3B82F6',
      text: 'A'
    },
    {
      name: 'testimonial-2.webp',
      width: 96,
      height: 96,
      color: '#10B981',
      text: 'S'
    },
    {
      name: 'testimonial-3.webp',
      width: 96,
      height: 96,
      color: '#F59E0B',
      text: 'B'
    },
    {
      name: 'product-sample.webp',
      width: 400,
      height: 300,
      color: '#8B5CF6',
      text: 'GENSET'
    }
  ];

  for (const sample of samples) {
    const outputPath = path.join(imagesDir, sample.name);
    
    if (fs.existsSync(outputPath)) {
      console.log(`⏭️  Skipping ${sample.name} (already exists)`);
      continue;
    }

    try {
      // Create a simple colored rectangle with text
      const svg = `
        <svg width="${sample.width}" height="${sample.height}" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="${sample.color}"/>
          <text x="50%" y="50%" font-family="Arial" font-size="24" font-weight="bold" 
                text-anchor="middle" dominant-baseline="middle" fill="white">
            ${sample.text}
          </text>
        </svg>
      `;

      await sharp(Buffer.from(svg))
        .webp({ quality: 90 })
        .toFile(outputPath);
      
      console.log(`✅ Generated ${sample.name}`);
    } catch (error) {
      console.error(`❌ Error generating ${sample.name}:`, error.message);
    }
  }
}

// Main function
async function main() {
  console.log('🚀 WebP Conversion Tool');
  console.log('========================\n');

  // Check if images directory exists
  if (!fs.existsSync(imagesDir)) {
    console.log('📁 Images directory not found, creating...');
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  // Generate sample images if no images exist
  const existingFiles = fs.existsSync(imagesDir) ? fs.readdirSync(imagesDir) : [];
  const hasImages = existingFiles.some(file => {
    const ext = path.extname(file).toLowerCase();
    return supportedFormats.includes(ext) || ext === '.webp';
  });

  if (!hasImages) {
    await generateSampleImages();
  }

  // Convert existing images
  await processDirectory(imagesDir);

  console.log('\n🎉 WebP conversion completed!');
  console.log('\n💡 Tips:');
  console.log('   - Use WebPImage component for automatic fallback');
  console.log('   - Original images are kept for compatibility');
  console.log('   - WebP images are served when supported');
  console.log('   - Run this script again to convert new images');
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { convertToWebP, processDirectory };
