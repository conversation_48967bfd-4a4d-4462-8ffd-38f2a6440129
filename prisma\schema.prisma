generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  directUrl    = env("DATABASE_URL_UNPOOLED")
  relationMode = "prisma"
}

model User {
  id            String                 @id @default(cuid())
  name          String?
  email         String                 @unique
  image         String?
  password      String?
  phone         String?
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt
  role          UserRole               @default(USER)
  products      Product[]
  rentals       Rental[]
  statusHistory PaymentStatusHistory[]
  notifications Notification[]

  @@map("users")
}

model Product {
  id           String        @id @default(cuid())
  name         String
  price        Float
  description  String?
  image        String?
  imageUrl     String?
  capacity     Int
  stock        Int           @default(0)
  status       ProductStatus @default(AVAILABLE)
  userId       String        @map("user_id")
  category     String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  overtimeRate Float?
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  rentals      Rental[]

  @@index([status])
  @@index([userId])
}

model Rental {
  id               String       @id @default(cuid())
  userId           String       @map("user_id")
  productId        String       @map("product_id")
  startDate        DateTime     @map("start_date")
  endDate          DateTime     @map("end_date")
  arrivalTime      String       @map("arrival_time")
  purpose          String
  amount           Float        @default(0)
  createdAt        DateTime     @default(now()) @map("created_at")
  updatedAt        DateTime     @updatedAt @map("updated_at")
  operationalStart DateTime?
  operationalEnd   DateTime?
  overtimeHours    Float?       @default(0)
  address          String?
  duration         String?
  notes            String?
  status           RentalStatus @default(PENDING)
  user             User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  product          Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  payment          Payment?

  @@index([userId])
  @@index([productId])
  @@map("rentals")
}

model Payment {
  id                   String                 @id @default(cuid())
  rentalId             String                 @unique
  amount               Float
  deposit              Float
  remaining            Float                  @default(0)
  overtimeCost         Float?
  transactionId        String?
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  status               PaymentStatus          @default(DEPOSIT_PENDING)
  rental               Rental                 @relation(fields: [rentalId], references: [id])
  PaymentStatusHistory PaymentStatusHistory[]

  @@index([rentalId])
}

model PaymentStatusHistory {
  id        String        @id @default(cuid())
  paymentId String        @map("payment_id")
  userId    String        @map("user_id")
  createdAt DateTime      @default(now())
  newStatus PaymentStatus
  payment   Payment       @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  user      User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([paymentId])
  @@index([userId])
  @@map("payment_status_history")
}

model verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime @map("expires_at")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@unique([identifier, value])
  @@map("verification")
}

model account {
  id                    String    @id @default(cuid())
  accountId             String    @map("account_id")
  providerId            String    @map("provider_id")
  userId                String    @map("user_id")
  type                  String    @default("credential")
  accessToken           String?   @map("access_token")
  refreshToken          String?   @map("refresh_token")
  idToken               String?   @map("id_token")
  accessTokenExpiresAt  DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt DateTime? @map("refresh_token_expires_at")
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  @@unique([providerId, accountId])
  @@map("account")
}

model session {
  id        String   @id @default(cuid())
  expiresAt DateTime @map("expires_at")
  token     String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  userId    String   @map("user_id")

  @@map("session")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  type      NotificationType
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("notifications")
}

enum UserRole {
  USER
  ADMIN
}

enum ProductStatus {
  AVAILABLE
  NOT_AVAILABLE
  MAINTENANCE
}

enum RentalStatus {
  PENDING
  CONFIRMED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  DEPOSIT_PENDING
  DEPOSIT_PAID
  FULLY_PAID
  FAILED
  INVOICE_ISSUED
}

enum NotificationType {
  PAYMENT_SUCCESS
  PAYMENT_FAILED
  NEW_RENTAL
  RENTAL_CONFIRMED
  OPERATION_STARTED
  OPERATION_COMPLETED
  LOW_STOCK
  OVERTIME_DETECTED
  NEW_PAYMENT
  NEW_INVOICE
}
