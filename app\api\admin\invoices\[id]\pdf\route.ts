import { NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { formatCurrency, formatDate } from "@/lib/utils/format";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }
    
    const { id } = await params;
    
    // Get payment data with related information
    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        rental: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true
              }
            },
            product: true
          }
        }
      }
    });
    
    if (!payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }
    
    const invoiceNumber = `INV-${payment.id.substring(0, 8)}`;
    const rental = payment.rental;
    const user = rental.user;
    const product = rental.product;
    
    // Create HTML template for PDF generation
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Invoice ${invoiceNumber}</title>
          <style>
            @page {
              size: A4;
              margin: 20mm;
            }
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 0;
              color: #333;
              font-size: 12px;
              line-height: 1.4;
            }
            .header {
              display: flex;
              justify-content: space-between;
              align-items: start;
              margin-bottom: 30px;
              border-bottom: 2px solid #2563eb;
              padding-bottom: 20px;
            }
            .company-info h1 {
              color: #2563eb;
              font-size: 24px;
              margin: 0 0 10px 0;
            }
            .company-info p {
              margin: 2px 0;
              color: #666;
            }
            .invoice-info {
              text-align: right;
            }
            .invoice-info h2 {
              color: #2563eb;
              font-size: 20px;
              margin: 0 0 10px 0;
            }
            .customer-info {
              margin-bottom: 30px;
            }
            .customer-info h3 {
              color: #2563eb;
              border-bottom: 1px solid #ddd;
              padding-bottom: 5px;
              margin-bottom: 10px;
            }
            .details-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
            }
            .details-table th,
            .details-table td {
              border: 1px solid #ddd;
              padding: 12px;
              text-align: left;
            }
            .details-table th {
              background-color: #f8f9fa;
              font-weight: bold;
              color: #2563eb;
            }
            .details-table .text-right {
              text-align: right;
            }
            .totals {
              width: 50%;
              margin-left: auto;
              margin-bottom: 30px;
            }
            .totals table {
              width: 100%;
              border-collapse: collapse;
            }
            .totals td {
              padding: 8px 12px;
              border-bottom: 1px solid #ddd;
            }
            .totals .total-row {
              font-weight: bold;
              background-color: #f8f9fa;
              color: #2563eb;
            }
            .notes {
              background-color: #f8f9fa;
              padding: 20px;
              border-left: 4px solid #2563eb;
              margin-bottom: 30px;
            }
            .notes h3 {
              color: #2563eb;
              margin-top: 0;
            }
            .footer {
              text-align: center;
              color: #666;
              font-size: 10px;
              border-top: 1px solid #ddd;
              padding-top: 20px;
            }
            .status-badge {
              display: inline-block;
              padding: 6px 12px;
              border-radius: 4px;
              font-weight: bold;
              font-size: 10px;
              text-transform: uppercase;
            }
            .status-pending {
              background-color: #fef3c7;
              color: #92400e;
            }
            .status-paid {
              background-color: #d1fae5;
              color: #065f46;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-info">
              <h1>RENTAL GENSET</h1>
              <p>Jl. Contoh No. 123, Jakarta</p>
              <p>Telp: 021-1234567</p>
              <p>Email: <EMAIL></p>
            </div>
            <div class="invoice-info">
              <h2>INVOICE</h2>
              <p><strong>${invoiceNumber}</strong></p>
              <p>Tanggal: ${formatDate(new Date())}</p>
              <p>Status: <span class="status-badge ${payment.status === 'FULLY_PAID' ? 'status-paid' : 'status-pending'}">${payment.status === 'FULLY_PAID' ? 'LUNAS' : 'PENDING'}</span></p>
            </div>
          </div>

          <div class="customer-info">
            <h3>INFORMASI PELANGGAN</h3>
            <p><strong>${user.name}</strong></p>
            <p>${user.email || '-'}</p>
            <p>${user.phone || '-'}</p>
            <p>${rental.address}</p>
          </div>

          <table class="details-table">
            <thead>
              <tr>
                <th>Deskripsi</th>
                <th class="text-right">Jumlah</th>
                <th class="text-right">Harga</th>
                <th class="text-right">Total</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <strong>${product.name} - ${product.capacity} KVA</strong><br>
                  <small>Tanggal: ${formatDate(rental.startDate)} - ${formatDate(rental.endDate)}</small><br>
                  <small>Durasi: ${rental.duration ? rental.duration.replace('_HOURS', '').replace('x8', ' x 8 Jam') : '-'}</small>
                </td>
                <td class="text-right">1x</td>
                <td class="text-right">${formatCurrency(product.price)}</td>
                <td class="text-right">${formatCurrency(rental.amount)}</td>
              </tr>
            </tbody>
          </table>

          <div class="totals">
            <table>
              <tr>
                <td>Total</td>
                <td class="text-right">${formatCurrency(rental.amount)}</td>
              </tr>
              <tr>
                <td>Deposit (50%)</td>
                <td class="text-right">${formatCurrency(payment.deposit)}</td>
              </tr>
              <tr>
                <td>Sisa Pembayaran (50%)</td>
                <td class="text-right">${formatCurrency(payment.remaining)}</td>
              </tr>
              ${payment.overtimeCost ? `
              <tr>
                <td>Biaya Overtime</td>
                <td class="text-right">${formatCurrency(payment.overtimeCost)}</td>
              </tr>
              ` : ''}
              <tr class="total-row">
                <td><strong>Total Tagihan</strong></td>
                <td class="text-right"><strong>${formatCurrency(payment.amount + (payment.overtimeCost || 0))}</strong></td>
              </tr>
            </table>
          </div>

          <div class="notes">
            <h3>CATATAN</h3>
            <p>Mohon melakukan pembayaran deposit sebesar ${formatCurrency(payment.deposit)} sebelum tanggal ${formatDate(rental.startDate)}. Sisa pembayaran dilakukan setelah operasi selesai.</p>
            <p><strong>Metode Pembayaran:</strong> Transfer Bank</p>
            <p><strong>Bank BCA:</strong> ********** a.n. PT Rental Genset</p>
          </div>

          <div class="footer">
            <p>Invoice ini dibuat secara otomatis dan sah tanpa tanda tangan.</p>
            <p>Jika ada pertanyaan tentang invoice ini, silakan hubungi <NAME_EMAIL></p>
            <p>Rental Genset &copy; ${new Date().getFullYear()}</p>
          </div>
        </body>
      </html>
    `;
    
    return new NextResponse(html, {
      headers: {
        "Content-Type": "text/html",
        "Content-Disposition": `inline; filename="invoice-${invoiceNumber}.html"`
      }
    });
    
  } catch (error) {
    console.error("Error generating invoice PDF:", error);
    return NextResponse.json(
      { error: "Failed to generate invoice PDF" },
      { status: 500 }
    );
  }
}
