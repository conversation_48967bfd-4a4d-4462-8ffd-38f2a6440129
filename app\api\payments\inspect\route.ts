import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";

export async function GET(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get ALL payments for this user to see what's happening
    const allPayments = await prisma.payment.findMany({
      where: {
        rental: {
          userId: session.user.id
        }
      },
      include: {
        rental: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Also get raw SQL data to compare
    const rawPayments = await prisma.$queryRaw`
      SELECT 
        p.id,
        p."rentalId",
        p.status,
        p.amount,
        p.deposit,
        p.remaining,
        p."overtimeCost",
        p."transactionId",
        p."createdAt",
        p."updatedAt",
        r.status as rental_status,
        r.amount as rental_amount,
        u.name as user_name,
        pr.name as product_name
      FROM "Payment" p
      JOIN "rentals" r ON p."rentalId" = r.id
      JOIN "users" u ON r."userId" = u.id
      JOIN "Product" pr ON r."productId" = pr.id
      WHERE r."userId" = ${session.user.id}
      ORDER BY p."createdAt" DESC
    `;

    // Find the specific payment that starts with CMC9792J
    const targetPaymentPrisma = allPayments.find(p => 
      p.id.toUpperCase().startsWith('CMC9792J')
    );

    const targetPaymentRaw = Array.isArray(rawPayments) ? 
      rawPayments.find((p: any) => p.id.toUpperCase().startsWith('CMC9792J')) : null;

    return NextResponse.json({
      success: true,
      sessionUser: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role
      },
      totalPayments: allPayments.length,
      allPaymentsPrisma: allPayments.map(p => ({
        id: p.id,
        paymentCode: `#${p.id.substring(0, 8).toUpperCase()}`,
        status: p.status,
        amount: p.amount,
        deposit: p.deposit,
        remaining: p.remaining,
        overtimeCost: p.overtimeCost,
        transactionId: p.transactionId,
        createdAt: p.createdAt,
        updatedAt: p.updatedAt,
        rental: {
          id: p.rental.id,
          status: p.rental.status,
          amount: p.rental.amount,
          user: p.rental.user,
          product: p.rental.product
        }
      })),
      allPaymentsRaw: rawPayments,
      targetPaymentPrisma,
      targetPaymentRaw,
      // Debug info about the specific payment
      debugInfo: targetPaymentPrisma ? {
        paymentId: targetPaymentPrisma.id,
        paymentCode: `#${targetPaymentPrisma.id.substring(0, 8).toUpperCase()}`,
        currentStatus: targetPaymentPrisma.status,
        shouldShowProgress: targetPaymentPrisma.status === "FULLY_PAID" ? "100%" : 
                           targetPaymentPrisma.status === "DEPOSIT_PAID" ? "50%" : "0%",
        logicCheck: {
          isFullyPaid: targetPaymentPrisma.status === "FULLY_PAID",
          isDepositPaid: targetPaymentPrisma.status === "DEPOSIT_PAID",
          isDepositPending: targetPaymentPrisma.status === "DEPOSIT_PENDING",
          statusComparison: {
            exact: targetPaymentPrisma.status,
            lowercase: targetPaymentPrisma.status.toLowerCase(),
            uppercase: targetPaymentPrisma.status.toUpperCase()
          }
        }
      } : null
    });

  } catch (error) {
    console.error("[INSPECT_PAYMENTS]", error);
    return NextResponse.json(
      { 
        error: "Failed to inspect payments", 
        details: error.message,
        stack: error.stack 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { action, paymentId } = await request.json();

    if (action === "force_fix_cmc9792j") {
      console.log("[FORCE_FIX] Starting force fix for CMC9792J payment...");
      
      // Find the payment
      const payment = await prisma.payment.findFirst({
        where: {
          id: {
            startsWith: paymentId.toLowerCase()
          },
          rental: {
            userId: session.user.id
          }
        },
        include: {
          rental: true
        }
      });

      if (!payment) {
        return NextResponse.json({ error: "Payment not found" }, { status: 404 });
      }

      console.log("[FORCE_FIX] Found payment:", payment.id, "Current status:", payment.status);

      // Force update using raw SQL to bypass any potential Prisma issues
      const updateResult = await prisma.$executeRaw`
        UPDATE "Payment" 
        SET 
          status = 'DEPOSIT_PAID',
          "transactionId" = ${'FORCE_FIX_' + Date.now()},
          "updatedAt" = NOW()
        WHERE id = ${payment.id}
      `;

      const rentalUpdateResult = await prisma.$executeRaw`
        UPDATE "rentals" 
        SET 
          status = 'ACTIVE',
          "updatedAt" = NOW()
        WHERE id = ${payment.rentalId}
      `;

      console.log("[FORCE_FIX] Update results:", { updateResult, rentalUpdateResult });

      // Verify the update
      const verifyPayment = await prisma.payment.findUnique({
        where: { id: payment.id },
        include: { rental: true }
      });

      return NextResponse.json({
        success: true,
        message: "Force fix completed",
        originalStatus: payment.status,
        newStatus: verifyPayment?.status,
        updateResult,
        rentalUpdateResult,
        verifyPayment
      });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });

  } catch (error) {
    console.error("[FORCE_FIX_ERROR]", error);
    return NextResponse.json(
      { 
        error: "Failed to force fix payment", 
        details: error.message,
        stack: error.stack 
      },
      { status: 500 }
    );
  }
}
