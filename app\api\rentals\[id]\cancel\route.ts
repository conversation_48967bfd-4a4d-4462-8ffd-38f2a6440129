import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { revalidatePath } from "next/cache";
import { updateProductStock } from "@/lib/actions/product";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();

    // Only admin can cancel rentals
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { id: rentalId } = await params;
    
    // Validasi body request
    const body = await request.json();
    const { reason } = body;
    
    // Cek apakah rental ada
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      select: {
        id: true,
        status: true,
        productId: true,

        product: {
          select: {
            id: true,
            stock: true
          }
        }
      }
    });
    
    if (!rental) {
      return NextResponse.json({ error: "Rental tidak ditemukan" }, { status: 404 });
    }
    
    // Hanya bisa membatalkan rental yang berstatus pending atau confirmed
    if (rental.status !== "PENDING" && rental.status !== "CONFIRMED") {
      return NextResponse.json(
        { error: "Hanya rental dengan status menunggu atau dikonfirmasi yang dapat dibatalkan" },
        { status: 400 }
      );
    }
    
    // 1. Update status rental menjadi cancelled
    await prisma.rental.update({
      where: { id: rentalId },
      data: {
        status: "CANCELLED",
        notes: reason ? (rental.status === "CONFIRMED" ? reason : reason) : undefined
      }
    });
    
    console.log(`[CANCEL] Rental ${rentalId} status diubah dari "${rental.status}" menjadi "cancelled"`);
    
    // 2. Jika status rental adalah confirmed, kembalikan stok produk
    // Catatan: Stok hanya dikembalikan jika status sebelumnya adalah "confirmed"
    // Jika status sebelumnya adalah "pending", tidak perlu mengembalikan stok karena belum dikurangi
    if (rental.status === "CONFIRMED") {
      console.log(`[CANCEL] Rental dengan status "confirmed" dibatalkan, mengembalikan stok +1 unit`);
      const stockUpdateResult = await updateProductStock(rental.productId, 1);

      if (!stockUpdateResult.success) {
        console.error(`[CANCEL] Gagal mengembalikan stok: ${stockUpdateResult.error}`);
        // Tetap lanjutkan proses meskipun ada error pada stok
      } else {
        console.log(`[CANCEL] Stok berhasil dikembalikan. Stok baru: ${stockUpdateResult.data?.stock}`);
      }
    } else {
      console.log(`[CANCEL] Rental dengan status "${rental.status}" dibatalkan, tidak perlu mengembalikan stok`);
    }
    
    // 3. Update status pembayaran jika ada
    await prisma.payment.updateMany({
      where: { rentalId: rentalId },
      data: {
        status: "FAILED",
      }
    });
    
    console.log(`[CANCEL] Payment untuk rental ${rentalId} diubah menjadi cancelled`);
    
    // Revalidasi path terkait
    revalidatePath("/admin/rentals");
    revalidatePath(`/admin/rentals/${rentalId}`);
    revalidatePath("/admin/products"); // Revalidasi halaman produk untuk melihat perubahan stok
    
    return NextResponse.json({ 
      success: true, 
      message: "Rental berhasil dibatalkan" 
    });
  } catch (error) {
    console.error("Error cancelling rental:", error);
    return NextResponse.json(
      { error: "Gagal membatalkan rental" },
      { status: 500 }
    );
  }
}
