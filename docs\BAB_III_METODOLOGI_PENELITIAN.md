# BAB III

# DESAIN SISTEM DAN PERANCANGAN PROGRAM

## 3.1 Metodologi

Penelitian ini menggunakan pendekatan kuantitatif dengan metode Research and Development (R&D) yang bertujuan untuk menghasilkan produk berupa sistem informasi rental genset berbasis web. Metodologi pengembangan sistem mengadopsi model System Development Life Cycle (SDLC) dengan pendekatan Waterfall yang disesuaikan dengan kebutuhan pengembangan sistem informasi berbasis web.

### 3.1.1 Model Waterfall

Model Waterfall adalah metodologi pengembangan perangkat lunak yang mengikuti pendekatan sekuensial dan linear. Men<PERSON><PERSON> Royce (1970), model Waterfall terdiri dari serangkaian fase yang harus diselesaikan secara berurutan sebelum melanjutkan ke fase berikutnya.

**Gambar 3.1 Model SDLC Waterfall**

![Model SDLC Waterfall](gambar/gambar-3-1-model-sdlc-waterfall.png)

_Sumber: <PERSON> (1970), diadaptasi untuk penelitian ini_

**Karakteristik Model Waterfall:**

1. **Sequential Process**: Setiap fase harus diselesaikan sepenuhnya sebelum memulai fase berikutnya
2. **Documentation Driven**: Setiap fase menghasilkan dokumentasi yang menjadi input untuk fase selanjutnya
3. **Milestone Oriented**: Setiap fase memiliki milestone yang jelas dan terukur
4. **Quality Control**: Setiap fase memiliki review dan approval sebelum melanjutkan
5. **Predictable Timeline**: Timeline yang dapat diprediksi dengan baik

**Kelebihan Model Waterfall:**

- Struktur yang jelas dan mudah dipahami
- Dokumentasi yang lengkap di setiap fase
- Mudah untuk mengelola dan mengontrol proyek
- Cocok untuk proyek dengan requirement yang stabil
- Memungkinkan estimasi waktu dan biaya yang akurat

**Kekurangan Model Waterfall:**

- Kurang fleksibel terhadap perubahan requirement
- Testing dilakukan di akhir siklus pengembangan
- Customer hanya melihat hasil di akhir proyek
- Risiko tinggi jika terjadi kesalahan di fase awal

**Justifikasi Pemilihan Model Waterfall:**

Model Waterfall dipilih untuk pengembangan sistem informasi rental genset ini karena:

1. **Requirement yang Jelas**: Kebutuhan sistem rental genset sudah terdefinisi dengan baik
2. **Proyek Skala Menengah**: Ukuran proyek yang tidak terlalu kompleks
3. **Timeline yang Terbatas**: Waktu pengembangan yang terbatas memerlukan struktur yang jelas
4. **Tim Kecil**: Tim pengembangan yang kecil memerlukan metodologi yang sederhana
5. **Dokumentasi Penting**: Kebutuhan dokumentasi yang lengkap untuk keperluan akademik

### 3.1.2 Tahapan Model Waterfall

**Gambar 3.2 Tahapan Pengembangan Sistem**

![Tahapan Pengembangan Sistem](gambar/gambar-3-2-tahapan-pengembangan-sistem.png)

_Sumber: Hasil Penelitian (2025)_

Model Waterfall yang digunakan dalam penelitian ini terdiri dari enam tahapan utama:

**1. Requirements Analysis (Analisis Kebutuhan)**

- **Input**: Masalah bisnis, kebutuhan stakeholder
- **Proses**: Identifikasi dan dokumentasi kebutuhan fungsional dan non-fungsional
- **Output**: Software Requirements Specification (SRS)
- **Durasi**: 2-3 minggu

**2. System Design (Perancangan Sistem)**

- **Input**: SRS dari fase sebelumnya
- **Proses**: Perancangan arsitektur sistem, database, dan interface
- **Output**: System Design Document (SDD)
- **Durasi**: 3-4 minggu

**3. Implementation (Implementasi)**

- **Input**: SDD dari fase sebelumnya
- **Proses**: Coding dan pengembangan sistem berdasarkan desain
- **Output**: Source code dan executable system
- **Durasi**: 8-10 minggu

**4. Integration & Testing (Integrasi & Pengujian)**

- **Input**: Source code dari fase implementasi
- **Proses**: Unit testing, integration testing, system testing
- **Output**: Tested system dan test reports
- **Durasi**: 2-3 minggu

**5. Deployment (Penerapan)**

- **Input**: Tested system dari fase testing
- **Proses**: Instalasi sistem di environment produksi
- **Output**: Deployed system yang siap digunakan
- **Durasi**: 1-2 minggu

**6. Maintenance (Pemeliharaan)**

- **Input**: Deployed system dan user feedback
- **Proses**: Bug fixing, enhancement, dan support
- **Output**: Updated system dan maintenance reports
- **Durasi**: Ongoing

Metodologi yang digunakan dalam penelitian ini mengikuti tahapan-tahapan sistematis untuk memastikan pengembangan sistem yang terstruktur dan berkualitas. Setiap tahapan memiliki input, proses, dan output yang jelas untuk mencapai tujuan pengembangan sistem informasi rental genset berbasis web.

## 3.2 Tinjauan Organisasi

### 3.2.1 Profil Organisasi

Objek penelitian ini adalah bisnis rental genset yang beroperasi di wilayah Jakarta dan sekitarnya. Bisnis ini telah beroperasi selama lebih dari 5 tahun dan melayani berbagai segmen pelanggan mulai dari individu, perusahaan kecil, hingga proyek konstruksi besar.

### 3.2.2 Struktur Organisasi

Struktur organisasi bisnis rental genset terdiri dari:

**1. Pemilik/Direktur**

- Bertanggung jawab atas kebijakan strategis dan pengembangan bisnis
- Mengawasi operasional secara keseluruhan
- Membuat keputusan investasi dan ekspansi

**2. Manager Operasional**

- Mengelola operasional harian rental genset
- Mengkoordinasikan tim lapangan dan administrasi
- Bertanggung jawab atas customer service dan quality control

**3. Staff Administrasi**

- Menangani pemesanan dan pembayaran
- Membuat laporan keuangan dan inventori
- Mengelola dokumentasi dan arsip

**4. Teknisi/Operator**

- Melakukan maintenance dan perbaikan genset
- Menangani pengiriman dan pengambilan genset
- Memastikan kondisi genset dalam keadaan optimal

**5. Driver/Kurir**

- Menangani pengiriman genset ke lokasi customer
- Melakukan instalasi dan pengambilan genset
- Bertanggung jawab atas keamanan genset selama transportasi

### 3.2.3 Visi Organisasi

"Menjadi penyedia layanan rental genset terdepan di Indonesia yang memberikan solusi energi terpercaya, berkualitas tinggi, dan mudah diakses untuk mendukung kebutuhan listrik darurat dan proyek-proyek pembangunan."

### 3.2.4 Misi Organisasi

1. Menyediakan genset berkualitas tinggi dengan berbagai kapasitas untuk memenuhi kebutuhan beragam customer
2. Memberikan layanan rental yang cepat, responsif, dan profesional dengan dukungan teknisi berpengalaman
3. Mengembangkan teknologi informasi untuk meningkatkan kemudahan akses dan transparansi layanan
4. Membangun hubungan jangka panjang dengan customer melalui pelayanan yang memuaskan dan harga yang kompetitif
5. Berkontribusi dalam mendukung keberlangsungan proyek-proyek pembangunan dan kegiatan bisnis di Indonesia

### 3.2.5 Tujuan Organisasi

**Tujuan Jangka Pendek (1-2 tahun):**

- Meningkatkan efisiensi operasional melalui digitalisasi sistem rental
- Memperluas jangkauan customer melalui platform online
- Meningkatkan tingkat kepuasan customer hingga 95%
- Mengurangi waktu pemrosesan pesanan hingga 80%

**Tujuan Jangka Menengah (3-5 tahun):**

- Menjadi market leader rental genset di wilayah Jakarta dan sekitarnya
- Mengembangkan jaringan distribusi ke kota-kota besar lainnya
- Membangun sistem manajemen inventori yang terintegrasi
- Mencapai revenue growth 50% per tahun

**Tujuan Jangka Panjang (5-10 tahun):**

- Ekspansi ke seluruh Indonesia dengan sistem franchise
- Mengembangkan layanan rental equipment lainnya
- Menjadi perusahaan rental equipment terbesar di Indonesia
- Implementasi teknologi IoT untuk monitoring genset real-time

## 3.3 Identifikasi Masalah

### 3.3.1 Penjelasan Masalah

Berdasarkan observasi dan wawancara yang dilakukan terhadap bisnis rental genset, ditemukan beberapa permasalahan utama dalam sistem operasional yang sedang berjalan:

**1. Proses Pemesanan yang Tidak Efisien**
Sistem pemesanan masih dilakukan secara manual melalui telepon atau datang langsung ke lokasi. Hal ini menyebabkan:

- Customer harus menunggu lama untuk mendapatkan konfirmasi ketersediaan
- Proses pencatatan pesanan rentan terhadap kesalahan manusia
- Tidak ada sistem tracking status pesanan secara real-time
- Sulit untuk melakukan pemesanan di luar jam kerja

**2. Manajemen Inventori yang Tidak Optimal**
Pengelolaan inventori genset masih menggunakan catatan manual yang mengakibatkan:

- Informasi ketersediaan genset tidak akurat dan tidak real-time
- Sering terjadi double booking karena informasi tidak sinkron
- Sulit melakukan forecasting kebutuhan inventori
- Proses maintenance scheduling tidak terorganisir dengan baik

**3. Sistem Pembayaran yang Terbatas**
Metode pembayaran yang tersedia masih terbatas pada cash dan transfer manual:

- Customer harus datang langsung untuk melakukan pembayaran
- Proses verifikasi pembayaran memakan waktu lama
- Tidak ada sistem reminder untuk pembayaran yang jatuh tempo
- Pencatatan pembayaran masih manual dan rentan kesalahan

**4. Komunikasi yang Tidak Efektif**
Komunikasi antara penyedia layanan dan customer masih mengandalkan telepon dan SMS:

- Tidak ada notifikasi otomatis untuk update status pesanan
- Informasi perubahan jadwal atau masalah teknis tidak tersampaikan dengan cepat
- Customer tidak mendapat konfirmasi otomatis setelah melakukan pemesanan
- Sulit untuk broadcast informasi penting ke semua customer

**5. Pelaporan dan Analisis yang Terbatas**
Sistem pelaporan masih manual dan tidak comprehensive:

- Laporan keuangan dibuat secara manual setiap akhir bulan
- Tidak ada analisis trend pemesanan dan revenue
- Sulit untuk mengidentifikasi customer loyal dan pattern pemesanan
- Data historis tidak terorganisir dengan baik untuk pengambilan keputusan

### 3.3.2 Penyebab Masalah

Analisis terhadap penyebab masalah yang terjadi dalam sistem rental genset konvensional:

**1. Ketergantungan pada Proses Manual**

- Semua proses bisnis masih dilakukan secara manual tanpa dukungan teknologi
- Tidak ada sistem informasi yang terintegrasi untuk mengelola operasional
- Staff masih mengandalkan catatan fisik dan komunikasi verbal

**2. Keterbatasan Teknologi**

- Belum ada investasi dalam teknologi informasi untuk mendukung operasional
- Infrastruktur IT yang minimal dan tidak mendukung digitalisasi
- Kurangnya pemahaman tentang manfaat sistem informasi dalam bisnis

**3. Sumber Daya Manusia**

- Keterbatasan jumlah staff untuk menangani volume pemesanan yang meningkat
- Kurangnya skill teknologi dari staff yang ada
- Beban kerja yang tinggi karena semua proses dilakukan manual

**4. Sistem Komunikasi yang Tradisional**

- Masih mengandalkan telepon dan SMS untuk komunikasi dengan customer
- Tidak ada platform digital untuk interaksi dengan customer
- Informasi tidak terdokumentasi dengan baik

### 3.3.3 Analisis Alternatif Solusi Masalah

Berdasarkan identifikasi masalah dan penyebabnya, berikut adalah analisis alternatif solusi yang dapat diterapkan:

**Alternatif 1: Sistem Informasi Berbasis Desktop**

- **Kelebihan**: Mudah diimplementasikan, biaya relatif rendah, kontrol data terpusat
- **Kekurangan**: Akses terbatas pada lokasi tertentu, tidak mendukung mobile access, sulit untuk scaling
- **Kesimpulan**: Kurang sesuai karena customer membutuhkan akses yang fleksibel

**Alternatif 2: Aplikasi Mobile Native**

- **Kelebihan**: User experience optimal, akses mudah melalui smartphone, push notification
- **Kekurangan**: Biaya development tinggi, perlu develop untuk multiple platform, maintenance kompleks
- **Kesimpulan**: Biaya terlalu tinggi untuk skala bisnis saat ini

**Alternatif 3: Sistem Informasi Berbasis Web (Dipilih)**

- **Kelebihan**:
  - Dapat diakses dari berbagai device (desktop, tablet, mobile)
  - Biaya development dan maintenance relatif rendah
  - Mudah untuk update dan maintenance
  - Mendukung integrasi dengan berbagai service eksternal
  - Scalable sesuai pertumbuhan bisnis
- **Kekurangan**: Membutuhkan koneksi internet, performa tergantung pada browser
- **Kesimpulan**: Solusi paling optimal untuk kebutuhan bisnis rental genset

**Justifikasi Pemilihan Solusi:**
Sistem informasi berbasis web dipilih sebagai solusi karena:

1. Memberikan akses yang fleksibel untuk customer dan admin
2. Biaya implementasi yang reasonable untuk skala bisnis
3. Dapat diintegrasikan dengan payment gateway dan sistem notifikasi
4. Mendukung pertumbuhan bisnis di masa depan
5. Maintenance dan update yang relatif mudah

## 3.4 Analisis Kebutuhan

### 3.4.1 Analisis Kebutuhan Perangkat Lunak

Berdasarkan analisis masalah dan solusi yang dipilih, berikut adalah kebutuhan perangkat lunak untuk pengembangan sistem:

**1. Development Tools**

- **Code Editor**: Visual Studio Code dengan extensions untuk web development
- **Version Control**: Git dan GitHub untuk source code management
- **Package Manager**: npm atau yarn untuk dependency management
- **Browser**: Chrome, Firefox, Safari untuk testing cross-browser compatibility

**2. Frontend Technologies**

- **Framework**: Next.js 14 untuk full-stack React development
- **UI Library**: React 18 dengan TypeScript untuk type safety
- **Styling**: Tailwind CSS untuk responsive design
- **Component Library**: shadcn/ui untuk consistent UI components
- **State Management**: React hooks dan context untuk state management

**3. Backend Technologies**

- **Runtime**: Node.js 18+ untuk server-side JavaScript
- **API Framework**: Next.js API routes untuk RESTful API
- **Authentication**: Better Auth untuk secure authentication system
- **Validation**: Zod untuk schema validation

**4. Database dan ORM**

- **Database**: PostgreSQL untuk relational data storage
- **ORM**: Prisma untuk type-safe database operations
- **Database GUI**: Prisma Studio untuk database management

**5. External Services**

- **Payment Gateway**: Midtrans untuk online payment processing
- **Notification Service**: Fonnte API untuk WhatsApp notifications
- **Hosting**: Vercel untuk frontend deployment
- **Database Hosting**: Supabase atau Railway untuk PostgreSQL hosting

**6. Testing dan Quality Assurance**

- **Unit Testing**: Jest untuk unit testing
- **E2E Testing**: Cypress untuk end-to-end testing
- **API Testing**: Postman untuk API testing
- **Code Quality**: ESLint dan Prettier for code formatting

### 3.4.2 Analisis Kebutuhan Perangkat Keras

**1. Development Environment**

- **Laptop/PC**: Minimum Intel Core i5 atau AMD Ryzen 5, RAM 16GB, SSD 512GB
- **Monitor**: Dual monitor setup untuk productivity (optional)
- **Internet Connection**: Minimum 10 Mbps untuk development dan testing

**2. Production Environment**

- **Web Hosting**: Cloud hosting dengan minimum 2GB RAM, 2 CPU cores
- **Database Server**: PostgreSQL hosting dengan minimum 1GB RAM
- **CDN**: Content Delivery Network untuk static assets
- **SSL Certificate**: HTTPS encryption untuk security

**3. Client Requirements**

- **Customer Device**: Smartphone, tablet, atau computer dengan browser modern
- **Internet Connection**: Minimum 1 Mbps untuk optimal user experience
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### 3.4.3 Analisis Kebutuhan Sumber Daya Manusia

**1. Tim Pengembangan**

- **Full-Stack Developer**: 1 orang dengan expertise Next.js, React, PostgreSQL
- **UI/UX Designer**: 1 orang untuk design interface dan user experience (optional)
- **Project Manager**: 1 orang untuk koordinasi dan timeline management

**2. Tim Operasional**

- **System Administrator**: 1 orang untuk maintenance server dan database
- **Content Manager**: 1 orang untuk manage konten produk dan informasi
- **Customer Support**: 1-2 orang untuk handle customer inquiries

**3. Skill Requirements**

- **Technical Skills**: JavaScript/TypeScript, React, Next.js, PostgreSQL, API integration
- **Soft Skills**: Problem solving, communication, project management
- **Domain Knowledge**: Understanding bisnis rental dan customer needs

**4. Training Needs**

- **Admin Training**: Pelatihan penggunaan dashboard admin dan management system
- **Staff Training**: Pelatihan basic computer skills dan customer service digital
- **Maintenance Training**: Pelatihan basic troubleshooting dan system monitoring

## 3.5 Sistem Lama

Sistem rental genset yang berjalan saat ini masih menggunakan pendekatan konvensional dengan proses manual dalam berbagai aspek operasionalnya. Berikut adalah analisis sistem lama yang sedang berjalan:

### 3.5.1 Proses Bisnis Sistem Lama

**1. Proses Pemesanan**

- Pelanggan datang langsung ke lokasi atau menghubungi via telepon
- Staff mencatat pesanan secara manual di buku catatan
- Pengecekan ketersediaan dilakukan secara manual dengan melihat catatan fisik
- Konfirmasi pesanan dilakukan via telepon atau SMS

**2. Proses Pembayaran**

- Pembayaran hanya dapat dilakukan secara tunai atau transfer manual
- Bukti pembayaran berupa kwitansi tulis tangan
- Tidak ada sistem tracking pembayaran otomatis
- Proses verifikasi pembayaran memakan waktu lama

**3. Proses Inventori**

- Pencatatan stok dilakukan manual di buku inventori
- Update ketersediaan tidak real-time
- Sering terjadi double booking karena informasi tidak sinkron
- Laporan inventori dibuat manual setiap akhir bulan

**4. Komunikasi dengan Pelanggan**

- Komunikasi hanya via telepon atau SMS manual
- Tidak ada notifikasi otomatis untuk status pesanan
- Informasi update harus dikomunikasikan satu per satu
- Sering terjadi miscommunication

### 3.5.2 Kelemahan Sistem Lama

1. **Efisiensi Rendah**: Proses manual membutuhkan waktu lama dan tenaga banyak
2. **Prone to Error**: Pencatatan manual rentan terhadap kesalahan manusia
3. **Tidak Real-time**: Informasi ketersediaan tidak update secara real-time
4. **Sulit Diakses**: Pelanggan harus datang langsung atau menelepon untuk informasi
5. **Laporan Manual**: Pembuatan laporan memakan waktu dan tidak akurat
6. **Komunikasi Terbatas**: Tidak ada sistem notifikasi otomatis
7. **Skalabilitas Rendah**: Sulit untuk mengembangkan bisnis dengan sistem manual

## 3.6 Sistem Alternatif

Berdasarkan analisis kelemahan sistem lama, dirancang sistem alternatif berupa sistem informasi rental genset berbasis web yang dapat mengatasi permasalahan yang ada. Sistem alternatif ini menawarkan solusi digital yang terintegrasi dan otomatis.

### 3.6.1 Konsep Sistem Alternatif

**1. Platform Web-Based**

- Sistem dapat diakses 24/7 melalui browser web
- Interface responsif untuk desktop dan mobile
- Multi-user dengan role-based access control
- Real-time data synchronization

**2. Otomatisasi Proses**

- Pemesanan online dengan kalkulasi otomatis
- Payment gateway terintegrasi untuk pembayaran digital
- Notifikasi otomatis via WhatsApp
- Update inventori real-time

**3. Manajemen Data Terpusat**

- Database terpusat dengan PostgreSQL
- Backup otomatis dan data security
- Reporting dan analytics otomatis
- Data integrity dan consistency

### 3.6.2 Keunggulan Sistem Alternatif

1. **Efisiensi Tinggi**: Otomatisasi proses mengurangi waktu dan tenaga
2. **Akurasi Data**: Sistem digital mengurangi human error
3. **Real-time Information**: Update data secara real-time
4. **Aksesibilitas**: Dapat diakses kapan saja dan dimana saja
5. **Reporting Otomatis**: Laporan generated secara otomatis
6. **Komunikasi Terintegrasi**: Notifikasi WhatsApp otomatis
7. **Skalabilitas Tinggi**: Mudah dikembangkan seiring pertumbuhan bisnis
8. **Cost Effective**: Mengurangi biaya operasional jangka panjang

## 3.7 Normalisasi Database

Normalisasi database dilakukan untuk memastikan struktur database yang efisien, mengurangi redundansi data, dan menjaga integritas data. Proses normalisasi dilakukan hingga bentuk normal ketiga (3NF).

### 3.7.1 Bentuk Normal Pertama (1NF)

Pada tahap ini, semua atribut dalam tabel harus memiliki nilai atomik (tidak dapat dibagi lagi) dan tidak ada grup berulang.

**Tabel Tidak Normal:**

```
Rental(id, customer_name, customer_phone, customer_email, product_names, product_prices, rental_dates)
```

**Tabel 1NF:**

```
Rental(id, customer_name, customer_phone, customer_email, product_name, product_price, rental_date)
```

### 3.7.2 Bentuk Normal Kedua (2NF)

Pada tahap ini, tabel harus sudah dalam bentuk 1NF dan semua atribut non-key harus bergantung penuh pada primary key.

**Hasil 2NF:**

```
Customer(customer_id, customer_name, customer_phone, customer_email)
Product(product_id, product_name, product_price, product_type, product_capacity)
Rental(rental_id, customer_id, rental_date, return_date, total_amount)
RentalDetail(rental_id, product_id, quantity, subtotal)
```

### 3.7.3 Bentuk Normal Ketiga (3NF)

Pada tahap ini, tabel harus sudah dalam bentuk 2NF dan tidak ada ketergantungan transitif.

**Hasil 3NF (Final):**

```
User(id, name, email, phone, role, password, created_at, updated_at)
Product(id, name, type, capacity, price_per_day, overtime_rate, description, image, status, created_at, updated_at)
Rental(id, user_id, product_id, start_date, end_date, pickup_location, delivery_location, total_amount, deposit_amount, status, created_at, updated_at)
Payment(id, rental_id, amount, payment_type, payment_method, payment_status, midtrans_transaction_id, created_at, updated_at)
PaymentHistory(id, payment_id, status, amount, payment_date, notes, created_at, updated_at)
```

## 3.8 Entity-Relationship Diagram (ERD)

Entity-Relationship Diagram (ERD) menggambarkan hubungan antar entitas dalam sistem informasi rental genset. ERD ini menjadi dasar untuk implementasi database sistem.

**Gambar 3.3 Entity-Relationship Diagram**

![Entity-Relationship Diagram](gambar/gambar-3-3-entity-relationship-diagram.png)

_Sumber: Hasil Penelitian (2025)_

### 3.8.1 Entitas dan Atribut

**1. Entitas User**

- **Primary Key**: id
- **Atribut**: name, email, phone, role, password, created_at, updated_at
- **Deskripsi**: Menyimpan data pengguna sistem (admin dan customer)

**2. Entitas Product**

- **Primary Key**: id
- **Atribut**: name, type, capacity, price_per_day, overtime_rate, description, image, status, created_at, updated_at
- **Deskripsi**: Menyimpan data produk genset yang tersedia untuk disewa

**3. Entitas Rental**

- **Primary Key**: id
- **Foreign Key**: user_id, product_id
- **Atribut**: start_date, end_date, pickup_location, delivery_location, total_amount, deposit_amount, status, created_at, updated_at
- **Deskripsi**: Menyimpan data transaksi rental genset

**4. Entitas Payment**

- **Primary Key**: id
- **Foreign Key**: rental_id
- **Atribut**: amount, payment_type, payment_method, payment_status, midtrans_transaction_id, created_at, updated_at
- **Deskripsi**: Menyimpan data pembayaran rental

**5. Entitas PaymentHistory**

- **Primary Key**: id
- **Foreign Key**: payment_id
- **Atribut**: status, amount, payment_date, notes, created_at, updated_at
- **Deskripsi**: Menyimpan riwayat perubahan status pembayaran

### 3.8.2 Relasi Antar Entitas

**1. User - Rental (One to Many)**

- Satu user dapat memiliki banyak rental
- Relasi: user_id di tabel Rental sebagai foreign key

**2. Product - Rental (One to Many)**

- Satu product dapat disewa berkali-kali (dalam waktu berbeda)
- Relasi: product_id di tabel Rental sebagai foreign key

**3. Rental - Payment (One to One)**

- Setiap rental memiliki satu record pembayaran
- Relasi: rental_id di tabel Payment sebagai foreign key

**4. Payment - PaymentHistory (One to Many)**

- Satu payment dapat memiliki banyak history perubahan status
- Relasi: payment_id di tabel PaymentHistory sebagai foreign key

## 3.9 Data Flow Diagram (DFD)

Data Flow Diagram (DFD) menggambarkan aliran data dalam sistem informasi rental genset. DFD menunjukkan bagaimana data mengalir dari input hingga output melalui berbagai proses dalam sistem.

### 3.9.1 Context Diagram

Context diagram menggambarkan sistem secara keseluruhan dan interaksinya dengan entitas eksternal.

**Gambar 3.4 Context Diagram**

![Context Diagram](gambar/gambar-3-4-context-diagram.png)

_Sumber: Hasil Penelitian (2025)_

**Entitas Eksternal:**

1. **Customer**: Pengguna yang menyewa genset
2. **Admin**: Pengelola sistem rental genset
3. **Midtrans**: Payment gateway untuk pembayaran
4. **Fonnte**: Service untuk notifikasi WhatsApp

**Aliran Data:**

- Customer: Data registrasi, data pemesanan, data pembayaran
- Admin: Data produk, laporan, manajemen sistem
- Midtrans: Status pembayaran, konfirmasi transaksi
- Fonnte: Notifikasi WhatsApp

### 3.9.2 Data Flow Diagram Level 1

DFD Level 1 menggambarkan proses-proses utama dalam sistem rental genset.

**Gambar 3.5 Data Flow Diagram Level 1**

![Data Flow Diagram Level 1](gambar/gambar-3-5-data-flow-diagram-level-1.png)

_Sumber: Hasil Penelitian (2025)_

**Proses Utama:**

**1. Proses Manajemen User (1.0)**

- Input: Data registrasi, data login
- Output: Data user, status autentikasi
- Deskripsi: Mengelola registrasi, login, dan profil user

**2. Proses Manajemen Produk (2.0)**

- Input: Data produk, update stok
- Output: Informasi produk, ketersediaan
- Deskripsi: Mengelola data produk genset dan ketersediaannya

**3. Proses Pemesanan (3.0)**

- Input: Data pemesanan, pilihan produk
- Output: Konfirmasi pesanan, invoice
- Deskripsi: Memproses pemesanan rental genset

**4. Proses Pembayaran (4.0)**

- Input: Data pembayaran, konfirmasi Midtrans
- Output: Status pembayaran, receipt
- Deskripsi: Memproses pembayaran melalui payment gateway

**5. Proses Notifikasi (5.0)**

- Input: Data notifikasi, template pesan
- Output: Notifikasi WhatsApp
- Deskripsi: Mengirim notifikasi otomatis ke customer

**6. Proses Laporan (6.0)**

- Input: Data transaksi, filter laporan
- Output: Laporan keuangan, laporan operasional
- Deskripsi: Menghasilkan berbagai laporan untuk admin

**Data Store:**

- D1: Users (Data pengguna)
- D2: Products (Data produk)
- D3: Rentals (Data rental)
- D4: Payments (Data pembayaran)
- D5: PaymentHistory (Riwayat pembayaran)

## 3.10 Kamus Data

Kamus data berisi definisi dan spesifikasi detail dari setiap elemen data yang digunakan dalam sistem informasi rental genset. Kamus data membantu dalam memahami struktur, tipe, dan batasan setiap field dalam database.

### 3.10.1 Tabel User

| Field      | Tipe Data | Panjang | Keterangan             | Constraint               |
| ---------- | --------- | ------- | ---------------------- | ------------------------ |
| id         | String    | 36      | Primary key, UUID      | NOT NULL, UNIQUE         |
| name       | String    | 100     | Nama lengkap user      | NOT NULL                 |
| email      | String    | 100     | Email user             | NOT NULL, UNIQUE         |
| phone      | String    | 20      | Nomor telepon          | NOT NULL                 |
| role       | Enum      | -       | Role user (USER/ADMIN) | NOT NULL, DEFAULT 'USER' |
| password   | String    | 255     | Password terenkripsi   | NOT NULL                 |
| created_at | DateTime  | -       | Waktu pembuatan record | NOT NULL                 |
| updated_at | DateTime  | -       | Waktu update terakhir  | NOT NULL                 |

### 3.10.2 Tabel Product

| Field         | Tipe Data | Panjang | Keterangan                            | Constraint                    |
| ------------- | --------- | ------- | ------------------------------------- | ----------------------------- |
| id            | String    | 36      | Primary key, UUID                     | NOT NULL, UNIQUE              |
| name          | String    | 100     | Nama produk genset                    | NOT NULL                      |
| type          | String    | 50      | Tipe genset                           | NOT NULL                      |
| capacity      | String    | 20      | Kapasitas genset (KVA)                | NOT NULL                      |
| price_per_day | Decimal   | 10,2    | Harga sewa per hari                   | NOT NULL                      |
| overtime_rate | Decimal   | 10,2    | Tarif overtime per jam                | NOT NULL, DEFAULT 0           |
| description   | Text      | -       | Deskripsi produk                      | NULL                          |
| image         | String    | 255     | URL gambar produk                     | NULL                          |
| status        | Enum      | -       | Status (AVAILABLE/RENTED/MAINTENANCE) | NOT NULL, DEFAULT 'AVAILABLE' |
| created_at    | DateTime  | -       | Waktu pembuatan record                | NOT NULL                      |
| updated_at    | DateTime  | -       | Waktu update terakhir                 | NOT NULL                      |

### 3.10.3 Tabel Rental

| Field             | Tipe Data | Panjang | Keterangan                   | Constraint                  |
| ----------------- | --------- | ------- | ---------------------------- | --------------------------- |
| id                | String    | 36      | Primary key, UUID            | NOT NULL, UNIQUE            |
| user_id           | String    | 36      | Foreign key ke tabel User    | NOT NULL                    |
| product_id        | String    | 36      | Foreign key ke tabel Product | NOT NULL                    |
| start_date        | DateTime  | -       | Tanggal mulai rental         | NOT NULL                    |
| end_date          | DateTime  | -       | Tanggal selesai rental       | NOT NULL                    |
| pickup_location   | Text      | -       | Alamat pengambilan           | NOT NULL                    |
| delivery_location | Text      | -       | Alamat pengiriman            | NULL                        |
| total_amount      | Decimal   | 10,2    | Total biaya rental           | NOT NULL                    |
| deposit_amount    | Decimal   | 10,2    | Jumlah deposit (50%)         | NOT NULL                    |
| status            | Enum      | -       | Status rental                | NOT NULL, DEFAULT 'PENDING' |
| created_at        | DateTime  | -       | Waktu pembuatan record       | NOT NULL                    |
| updated_at        | DateTime  | -       | Waktu update terakhir        | NOT NULL                    |

### 3.10.4 Tabel Payment

| Field                   | Tipe Data | Panjang | Keterangan                     | Constraint                  |
| ----------------------- | --------- | ------- | ------------------------------ | --------------------------- |
| id                      | String    | 36      | Primary key, UUID              | NOT NULL, UNIQUE            |
| rental_id               | String    | 36      | Foreign key ke tabel Rental    | NOT NULL                    |
| amount                  | Decimal   | 10,2    | Jumlah pembayaran              | NOT NULL                    |
| payment_type            | Enum      | -       | Tipe pembayaran (DEPOSIT/FULL) | NOT NULL                    |
| payment_method          | String    | 50      | Metode pembayaran              | NOT NULL                    |
| payment_status          | Enum      | -       | Status pembayaran              | NOT NULL, DEFAULT 'PENDING' |
| midtrans_transaction_id | String    | 100     | ID transaksi Midtrans          | NULL                        |
| created_at              | DateTime  | -       | Waktu pembuatan record         | NOT NULL                    |
| updated_at              | DateTime  | -       | Waktu update terakhir          | NOT NULL                    |

### 3.10.5 Tabel PaymentHistory

| Field        | Tipe Data | Panjang | Keterangan                   | Constraint       |
| ------------ | --------- | ------- | ---------------------------- | ---------------- |
| id           | String    | 36      | Primary key, UUID            | NOT NULL, UNIQUE |
| payment_id   | String    | 36      | Foreign key ke tabel Payment | NOT NULL         |
| status       | String    | 50      | Status pembayaran            | NOT NULL         |
| amount       | Decimal   | 10,2    | Jumlah pembayaran            | NOT NULL         |
| payment_date | DateTime  | -       | Tanggal pembayaran           | NULL             |
| notes        | Text      | -       | Catatan tambahan             | NULL             |
| created_at   | DateTime  | -       | Waktu pembuatan record       | NOT NULL         |
| updated_at   | DateTime  | -       | Waktu update terakhir        | NOT NULL         |

## 3.11 Bagan Berjenjang

Bagan berjenjang menggambarkan struktur hierarki sistem informasi rental genset dari level tertinggi hingga level terendah. Bagan ini menunjukkan pembagian fungsi sistem secara terstruktur.

**Gambar 3.6 Bagan Berjenjang Sistem**

![Bagan Berjenjang Sistem](gambar/gambar-3-6-bagan-berjenjang-sistem.png)

_Sumber: Hasil Penelitian (2025)_

### 3.11.1 Level 0 - Sistem Informasi Rental Genset

**Sistem Informasi Rental Genset**

- Sistem utama yang mengelola seluruh proses rental genset

### 3.11.2 Level 1 - Modul Utama

**1. Modul Manajemen User**

- Mengelola registrasi, login, dan profil pengguna

**2. Modul Manajemen Produk**

- Mengelola data produk genset dan ketersediaan

**3. Modul Pemesanan**

- Mengelola proses pemesanan rental genset

**4. Modul Pembayaran**

- Mengelola proses pembayaran dan integrasi payment gateway

**5. Modul Notifikasi**

- Mengelola pengiriman notifikasi WhatsApp

**6. Modul Laporan**

- Mengelola pembuatan laporan dan analytics

### 3.11.3 Level 2 - Sub Modul

**Modul Manajemen User:**

- 1.1 Registrasi User
- 1.2 Login/Logout
- 1.3 Manajemen Profil
- 1.4 Manajemen Role

**Modul Manajemen Produk:**

- 2.1 CRUD Produk
- 2.2 Manajemen Stok
- 2.3 Manajemen Kategori
- 2.4 Upload Gambar

**Modul Pemesanan:**

- 3.1 Pencarian Produk
- 3.2 Pembuatan Pesanan
- 3.3 Konfirmasi Pesanan
- 3.4 Tracking Status

**Modul Pembayaran:**

- 4.1 Kalkulasi Biaya
- 4.2 Integrasi Midtrans
- 4.3 Verifikasi Pembayaran
- 4.4 Riwayat Pembayaran

**Modul Notifikasi:**

- 5.1 Template Pesan
- 5.2 Pengiriman WhatsApp
- 5.3 Log Notifikasi
- 5.4 Broadcast Message

**Modul Laporan:**

- 6.1 Laporan Transaksi
- 6.2 Laporan Keuangan
- 6.3 Laporan Inventori
- 6.4 Analytics Dashboard

## 3.12 Site Map

Site Map menggambarkan struktur navigasi dan hierarki halaman dalam sistem informasi rental genset berbasis web. Site Map membantu dalam memahami alur navigasi user dan admin dalam menggunakan sistem.

**Gambar 3.7 Site Map Sistem**

![Site Map Sistem](gambar/gambar-3-7-site-map-sistem.png)

_Sumber: Hasil Penelitian (2025)_

### 3.12.1 Struktur Navigasi Customer

**1. Halaman Publik**

- Home Page
  - Hero Section
  - Featured Products
  - About Us
  - Contact Info
- Product Catalog
  - Product List
  - Product Detail
  - Product Search & Filter
- About Us
- Contact Us

**2. Halaman Customer (Setelah Login)**

- Dashboard Customer
  - Profile Overview
  - Active Rentals
  - Rental History
- My Profile
  - Edit Profile
  - Change Password
- My Rentals
  - Current Rentals
  - Rental History
  - Rental Details
- New Rental
  - Product Selection
  - Rental Form
  - Payment Process
- Payment History
  - Payment List
  - Payment Details
  - Download Invoice

### 3.12.2 Struktur Navigasi Admin

**1. Dashboard Admin**

- Overview Statistics
- Recent Activities
- Quick Actions
- System Status

**2. User Management**

- User List
- User Details
- User Roles
- User Activities

**3. Product Management**

- Product List
- Add New Product
- Edit Product
- Product Categories
- Stock Management

**4. Rental Management**

- Rental List
- Rental Details
- Rental Status Update
- Rental Calendar

**5. Payment Management**

- Payment List
- Payment Details
- Payment Verification
- Refund Management

**6. Reports & Analytics**

- Financial Reports
- Rental Reports
- User Analytics
- Product Performance

**7. System Settings**

- General Settings
- Payment Gateway Config
- Notification Settings
- System Maintenance

## 3.13 Desain Layout

Desain layout menggambarkan rancangan antarmuka pengguna (user interface) untuk sistem informasi rental genset. Desain ini dibuat untuk memberikan pengalaman pengguna yang optimal baik untuk admin maupun customer.

### 3.13.1 Desain Layout Admin

**Gambar 3.8 Layout Dashboard Admin**

![Layout Dashboard Admin](gambar/gambar-3-8-layout-dashboard-admin.png)

_Sumber: Hasil Penelitian (2025)_

**Komponen Layout Admin:**

**1. Header Section**

- Logo sistem di sebelah kiri
- Breadcrumb navigation
- User profile dropdown di sebelah kanan
- Notification bell icon
- Logout button

**2. Sidebar Navigation**

- Dashboard menu
- User Management
- Product Management
- Rental Management
- Payment Management
- Reports & Analytics
- System Settings

**3. Main Content Area**

- Page title dan description
- Action buttons (Add, Export, etc.)
- Data tables dengan pagination
- Charts dan statistics
- Form inputs untuk CRUD operations

**4. Footer Section**

- Copyright information
- System version
- Last update timestamp

### 3.13.2 Desain Layout User/Customer

**Gambar 3.9 Layout Dashboard User**

![Layout Dashboard User](gambar/gambar-3-9-layout-dashboard-user.png)

_Sumber: Hasil Penelitian (2025)_

**Komponen Layout Customer:**

**1. Header Section**

- Logo dan brand name
- Main navigation menu (Home, Products, About, Contact)
- User account dropdown (jika login)
- Shopping cart icon
- Mobile hamburger menu

**2. Hero Section (Homepage)**

- Banner image dengan call-to-action
- Search bar untuk produk
- Featured products carousel
- Value propositions

**3. Content Area**

- Product grid dengan filter dan sort
- Product detail dengan image gallery
- Rental form dengan date picker
- Payment interface
- User dashboard dengan rental history

**4. Footer Section**

- Company information
- Contact details
- Social media links
- Terms & conditions

## 3.14 Desain Output

Desain output menggambarkan rancangan tampilan hasil keluaran dari sistem informasi rental genset. Output ini berupa laporan, invoice, dan notifikasi yang dihasilkan oleh sistem.

### 3.14.1 Desain Laporan Transaksi

**Gambar 3.10 Desain Laporan Transaksi**

![Desain Laporan Transaksi](gambar/gambar-3-10-desain-laporan-transaksi.png)

_Sumber: Hasil Penelitian (2025)_

**Komponen Laporan Transaksi:**

**1. Header Laporan**

- Logo perusahaan
- Judul laporan "LAPORAN TRANSAKSI RENTAL GENSET"
- Periode laporan (tanggal mulai - tanggal selesai)
- Tanggal cetak laporan

**2. Filter Informasi**

- Periode yang dipilih
- Status transaksi yang difilter
- Total jumlah transaksi
- Total nilai transaksi

**3. Tabel Data Transaksi**

- No urut
- ID Transaksi
- Tanggal Transaksi
- Nama Customer
- Produk yang disewa
- Durasi Rental
- Total Biaya
- Status Pembayaran
- Status Rental

**4. Summary Section**

- Total Transaksi
- Total Pendapatan
- Rata-rata Nilai Transaksi
- Transaksi Berhasil vs Dibatalkan

### 3.14.2 Desain Invoice

**Gambar 3.11 Desain Invoice**

![Desain Invoice](gambar/gambar-3-11-desain-invoice.png)

_Sumber: Hasil Penelitian (2025)_

**Komponen Invoice:**

**1. Header Invoice**

- Logo dan nama perusahaan
- Alamat perusahaan lengkap
- Nomor telepon dan email
- Judul "INVOICE RENTAL GENSET"

**2. Informasi Invoice**

- Nomor Invoice
- Tanggal Invoice
- Due Date
- Status Pembayaran

**3. Informasi Rental**

- ID Rental
- Tanggal Mulai Rental
- Tanggal Selesai Rental
- Lokasi Pengiriman
- Durasi Rental

**4. Detail Produk**

- Nama Produk
- Tipe dan Kapasitas
- Harga per Hari
- Jumlah Hari
- Subtotal

**5. Rincian Biaya**

- Biaya Rental
- Biaya Overtime (jika ada)
- Deposit (50%)
- Sisa Pembayaran (50%)
- Total Keseluruhan

**6. Informasi Pembayaran**

- Metode Pembayaran
- Status Pembayaran
- Tanggal Pembayaran
- Referensi Transaksi

**7. Footer**

- Terms & Conditions
- Tanda tangan digital
- Timestamp generate invoice

### 3.14.3 Desain Notifikasi WhatsApp

**Template Notifikasi:**

**1. Konfirmasi Pemesanan**

```
🎉 *KONFIRMASI PEMESANAN*

Halo [Nama Customer],

Pemesanan Anda telah berhasil dibuat!

📋 *Detail Pesanan:*
• ID: [Rental ID]
• Produk: [Nama Produk]
• Tanggal: [Start Date] - [End Date]
• Total: Rp [Total Amount]

💰 *Pembayaran:*
Silakan lakukan pembayaran deposit sebesar Rp [Deposit Amount] melalui link berikut:
[Payment Link]

Terima kasih! 🙏
```

**2. Konfirmasi Pembayaran**

```
✅ *PEMBAYARAN BERHASIL*

Halo [Nama Customer],

Pembayaran Anda telah berhasil diverifikasi!

💳 *Detail Pembayaran:*
• Jumlah: Rp [Amount]
• Metode: [Payment Method]
• Status: LUNAS

📦 *Pengiriman:*
Genset akan dikirim pada tanggal [Delivery Date] ke alamat yang telah ditentukan.

Terima kasih! 🙏
```

**3. Reminder Pembayaran**

```
⏰ *REMINDER PEMBAYARAN*

Halo [Nama Customer],

Kami mengingatkan bahwa pembayaran untuk rental genset Anda akan jatuh tempo dalam 24 jam.

📋 *Detail:*
• ID: [Rental ID]
• Jatuh Tempo: [Due Date]
• Jumlah: Rp [Amount]

Silakan lakukan pembayaran melalui:
[Payment Link]

Terima kasih! 🙏
```

Struktur BAB III ini telah disesuaikan dengan pedoman D3 RPL - Sistem Informasi Berbasis Web yang mencakup semua komponen yang diperlukan mulai dari metodologi, analisis sistem lama dan alternatif, normalisasi database, ERD, DFD, kamus data, bagan berjenjang, site map, desain layout hingga desain output.
