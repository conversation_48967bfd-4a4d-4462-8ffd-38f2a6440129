import { NextRequest, NextResponse } from 'next/server';
import { apiCacheUtils, cacheKeys } from '@/lib/utils/performance-cache';

// Cache configuration for different routes
const CACHE_CONFIG = {
  '/api/products': { ttl: 1000 * 60 * 10, key: 'products' }, // 10 minutes
  '/api/user/profile': { ttl: 1000 * 60 * 5, key: 'user-profile' }, // 5 minutes
  '/api/admin/dashboard': { ttl: 1000 * 60 * 2, key: 'admin-dashboard' }, // 2 minutes
  '/api/rentals': { ttl: 1000 * 60 * 3, key: 'rentals' }, // 3 minutes
  '/api/operations': { ttl: 1000 * 60 * 5, key: 'operations' }, // 5 minutes
};

// Generate cache key based on request
function generateCacheKey(request: NextRequest): string {
  const url = new URL(request.url);
  const pathname = url.pathname;
  const searchParams = url.searchParams.toString();
  const userId = request.headers.get('x-user-id') || 'anonymous';
  
  return `${pathname}:${userId}:${searchParams}`;
}

// Check if route should be cached
function shouldCache(pathname: string, method: string): boolean {
  // Only cache GET requests
  if (method !== 'GET') return false;
  
  // Check if route is in cache config
  return Object.keys(CACHE_CONFIG).some(route => pathname.startsWith(route));
}

// Get cache configuration for route
function getCacheConfig(pathname: string) {
  const route = Object.keys(CACHE_CONFIG).find(route => pathname.startsWith(route));
  return route ? CACHE_CONFIG[route as keyof typeof CACHE_CONFIG] : null;
}

// Cache middleware function
export async function cacheMiddleware(
  request: NextRequest,
  handler: (req: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  const pathname = new URL(request.url).pathname;
  const method = request.method;

  // Skip caching for non-GET requests or uncacheable routes
  if (!shouldCache(pathname, method)) {
    return handler(request);
  }

  const cacheKey = generateCacheKey(request);
  const config = getCacheConfig(pathname);

  if (!config) {
    return handler(request);
  }

  // Try to get from cache
  const cached = apiCacheUtils.get(cacheKey);
  if (cached) {
    console.log(`Cache HIT for ${cacheKey}`);
    
    const response = NextResponse.json(cached);
    response.headers.set('X-Cache', 'HIT');
    response.headers.set('Cache-Control', `public, max-age=${config.ttl / 1000}, stale-while-revalidate=300`);
    
    return response;
  }

  console.log(`Cache MISS for ${cacheKey}`);

  // Execute handler
  try {
    const response = await handler(request);
    
    // Only cache successful responses
    if (response.status === 200) {
      const responseData = await response.json();
      
      // Cache the response
      apiCacheUtils.set(cacheKey, responseData, config.ttl);
      
      // Create new response with cache headers
      const cachedResponse = NextResponse.json(responseData);
      cachedResponse.headers.set('X-Cache', 'MISS');
      cachedResponse.headers.set('Cache-Control', `public, max-age=${config.ttl / 1000}, stale-while-revalidate=300`);
      
      return cachedResponse;
    }
    
    return response;
  } catch (error) {
    console.error('Cache middleware error:', error);
    return handler(request);
  }
}

// Wrapper for API routes
export function withCache(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest) => {
    return cacheMiddleware(request, handler);
  };
}

// Cache invalidation utilities
export const cacheInvalidation = {
  // Invalidate user-specific cache
  invalidateUserCache: (userId: string) => {
    apiCacheUtils.invalidatePattern(`user:${userId}`);
    apiCacheUtils.invalidatePattern(`rentals:${userId}`);
    apiCacheUtils.invalidatePattern(`dashboard:${userId}`);
  },

  // Invalidate product cache
  invalidateProductCache: () => {
    apiCacheUtils.invalidatePattern('products');
    apiCacheUtils.invalidatePattern('product:');
  },

  // Invalidate admin cache
  invalidateAdminCache: () => {
    apiCacheUtils.invalidatePattern('admin');
    apiCacheUtils.invalidatePattern('operations');
  },

  // Invalidate all cache
  invalidateAll: () => {
    apiCacheUtils.clear();
  },
};

// Response compression middleware
export function compressionMiddleware(response: NextResponse): NextResponse {
  const contentType = response.headers.get('content-type');
  
  if (contentType?.includes('application/json')) {
    // Add compression headers for JSON responses
    response.headers.set('Content-Encoding', 'gzip');
    response.headers.set('Vary', 'Accept-Encoding');
  }
  
  return response;
}

// ETag middleware for conditional requests
export function etagMiddleware(request: NextRequest, response: NextResponse): NextResponse {
  const ifNoneMatch = request.headers.get('if-none-match');
  
  if (response.status === 200) {
    // Generate ETag based on response content
    const content = JSON.stringify(response);
    const etag = `"${Buffer.from(content).toString('base64').slice(0, 16)}"`;
    
    response.headers.set('ETag', etag);
    
    // Check if client has cached version
    if (ifNoneMatch === etag) {
      return new NextResponse(null, { status: 304 });
    }
  }
  
  return response;
}

// Combined middleware
export function performanceMiddleware(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest) => {
    // Apply cache middleware
    let response = await cacheMiddleware(request, handler);
    
    // Apply compression
    response = compressionMiddleware(response);
    
    // Apply ETag
    response = etagMiddleware(request, response);
    
    // Add performance headers
    response.headers.set('X-Response-Time', Date.now().toString());
    
    return response;
  };
}

// Rate limiting cache
const rateLimitCache = new Map<string, { count: number; resetTime: number }>();

export function rateLimitMiddleware(
  request: NextRequest,
  limit: number = 100,
  windowMs: number = 60000 // 1 minute
): boolean {
  const clientId = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const now = Date.now();
  
  const clientData = rateLimitCache.get(clientId);
  
  if (!clientData || now > clientData.resetTime) {
    // Reset or initialize
    rateLimitCache.set(clientId, {
      count: 1,
      resetTime: now + windowMs,
    });
    return true;
  }
  
  if (clientData.count >= limit) {
    return false; // Rate limit exceeded
  }
  
  clientData.count++;
  return true;
}

// Cleanup rate limit cache periodically
setInterval(() => {
  const now = Date.now();
  for (const [clientId, data] of rateLimitCache.entries()) {
    if (now > data.resetTime) {
      rateLimitCache.delete(clientId);
    }
  }
}, 60000); // Clean up every minute
