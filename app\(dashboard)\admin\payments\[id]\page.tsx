import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/app/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardT<PERSON>le, CardFooter } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { formatCurrency, formatDateTime } from "@/lib/utils/format";
import { ArrowLeft, Download, Clock, Check } from "lucide-react";
import { InvoiceActions } from "@/app/components/admin/invoice-actions";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

// Fungsi untuk mendapatkan tampilan status pembayaran
function getPaymentStatusBadge(status: string) {
  switch (status.toUpperCase()) {
    case "FULLY_PAID":
      return { label: "Lunas", variant: "default" as const, color: "bg-green-100 text-green-800" };
    case "DEPOSIT_PAID":
      return { label: "Deposit Dibayar", variant: "success" as const, color: "bg-blue-100 text-blue-800" };
    case "DEPOSIT_PENDING":
      return { label: "Menunggu Deposit", variant: "secondary" as const, color: "bg-yellow-100 text-yellow-800" };
    case "FAILED":
      return { label: "Gagal", variant: "destructive" as const, color: "bg-red-100 text-red-800" };
    default:
      return { label: "Menunggu", variant: "outline" as const, color: "bg-gray-100 text-gray-800" };
  }
}

export default async function PaymentDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getSession();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  const { id } = await params;

  if (!id) {
    redirect('/admin/payments');
  }

  // Ambil data pembayaran dari database
  const payment = await prisma.payment.findUnique({
    where: {
      id: id
    },
    include: {
      rental: {
        include: {
          product: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          }
        }
      }
    }
  });

  if (!payment) {
    redirect('/admin/payments');
  }

  const statusBadge = getPaymentStatusBadge(payment.status);
  const hasOvertime = payment.overtimeCost && payment.overtimeCost > 0;
  const isPending = payment.status === "DEPOSIT_PENDING";
  const isDepositPaid = payment.status === "DEPOSIT_PAID";
  const isCompleted = payment.rental.operationalEnd != null;
  const needsRemainingPayment = isDepositPaid && isCompleted;

  return (
    <div className="container mx-auto py-6">
      <Link href="/admin/payments" className="inline-flex items-center mb-6">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Kembali ke Daftar Pembayaran
      </Link>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Informasi Pembayaran</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">ID Pembayaran</span>
              <span className="font-mono">{payment.id.substring(0, 10)}...</span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Status</span>
              <Badge className={statusBadge.color}>
                {statusBadge.label}
              </Badge>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Total Pembayaran</span>
              <span className="font-medium">{formatCurrency(payment.amount)}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Deposit (50%)</span>
              <span className={isDepositPaid || payment.status === "FULLY_PAID" ? "text-green-600 font-medium" : ""}>
                {formatCurrency(payment.deposit)}
                {(isDepositPaid || payment.status === "FULLY_PAID") && " ✓"}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Sisa Pembayaran (50%)</span>
              <span className={payment.status === "FULLY_PAID" ? "text-green-600 font-medium" : ""}>
                {formatCurrency(payment.remaining)}
                {payment.status === "FULLY_PAID" && " ✓"}
              </span>
            </div>

            {hasOvertime && (
              <div className="flex justify-between">
                <span className="text-sm text-orange-600">Biaya Overtime</span>
                <span className="text-orange-600 font-medium">{formatCurrency(payment.overtimeCost || 0)}</span>
              </div>
            )}

            <div className="pt-2 border-t">
              <div className="flex justify-between">
                <span className="font-medium">Total Tagihan</span>
                <span className="font-bold text-lg">
                  {formatCurrency(payment.amount + (payment.overtimeCost || 0))}
                </span>
              </div>
            </div>

            {payment.transactionId && (
              <div className="flex justify-between pt-2 border-t">
                <span className="text-sm text-gray-500">ID Transaksi</span>
                <span className="font-mono text-xs">{payment.transactionId}</span>
              </div>
            )}

            {payment.updatedAt && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Terakhir Diperbarui</span>
                <span className="text-sm">{formatDateTime(payment.updatedAt)}</span>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col gap-3">
            {isPending && (
              <Link href={`/admin/payments/${payment.id}/create-invoice`} className="w-full">
                <Button className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Buat Invoice Deposit
                </Button>
              </Link>
            )}

            {needsRemainingPayment && (
              <Link href={`/admin/payments/${payment.id}/remaining-invoice`} className="w-full">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  <Download className="h-4 w-4 mr-2" />
                  Buat Invoice Sisa Pembayaran
                </Button>
              </Link>
            )}

            {payment.status === "FULLY_PAID" && (
              <>
                <div className="flex items-center justify-center w-full p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                  <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                  <span className="text-green-700 dark:text-green-300">Pembayaran telah lunas</span>
                </div>
                <InvoiceActions
                  invoiceId={payment.id}
                  customerPhone={payment.rental.user.phone || undefined}
                  customerEmail={payment.rental.user.email || undefined}
                />
              </>
            )}
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informasi Penyewaan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm text-gray-500 mb-1">Pelanggan</h3>
              <p className="font-medium">{payment.rental.user.name}</p>
              <p className="text-sm">{payment.rental.user.email}</p>
              <p className="text-sm">{payment.rental.user.phone}</p>
            </div>

            <div>
              <h3 className="text-sm text-gray-500 mb-1">Produk</h3>
              <p className="font-medium">{payment.rental.product.name}</p>
              <p className="text-sm">{payment.rental.product.capacity} KVA</p>
            </div>

            <div>
              <h3 className="text-sm text-gray-500 mb-1">Periode Sewa</h3>
              <p>{formatDateTime(payment.rental.startDate)} - {formatDateTime(payment.rental.endDate)}</p>
            </div>

            {payment.rental.operationalStart && (
              <div>
                <h3 className="text-sm text-gray-500 mb-1">Waktu Mulai Operasi</h3>
                <p>{formatDateTime(payment.rental.operationalStart)}</p>
              </div>
            )}

            {payment.rental.operationalEnd && (
              <div>
                <h3 className="text-sm text-gray-500 mb-1">Waktu Selesai Operasi</h3>
                <p>{formatDateTime(payment.rental.operationalEnd)}</p>
              </div>
            )}

            {payment.rental.status && (
              <div className="flex items-center mt-2 pt-2 border-t">
                <h3 className="text-sm text-gray-500 mr-2">Status Operasi:</h3>
                <Badge variant={payment.rental.status === "COMPLETED" ? "default" : payment.rental.status === "ACTIVE" ? "outline" : "secondary"}>
                  {payment.rental.status === "CONFIRMED" && "Dikonfirmasi"}
                  {payment.rental.status === "ACTIVE" && "Sedang Beroperasi"}
                  {payment.rental.status === "COMPLETED" && "Selesai"}
                  {payment.rental.status === "CANCELLED" && "Dibatalkan"}
                  {payment.rental.status === "PENDING" && "Menunggu"}
                </Badge>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <div className="w-full">
              <Link href={`/admin/operations/${payment.rental.id}`}>
                <Button variant="outline" className="w-full dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">
                  <Clock className="h-4 w-4 mr-2" />
                  Lihat Detail Operasi
                </Button>
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Riwayat Transaksi - Bisa ditambahkan di sini jika diperlukan */}
    </div>
  );
}