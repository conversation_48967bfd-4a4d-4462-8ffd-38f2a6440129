'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface WebPImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
}

// Generate WebP URL from original image path
function getWebPUrl(originalSrc: string): string {
  // If it's already a WebP, return as is
  if (originalSrc.endsWith('.webp')) {
    return originalSrc;
  }

  // If it's a placeholder or external URL, return as is
  if (originalSrc.startsWith('http') || originalSrc.includes('placeholder')) {
    return originalSrc;
  }

  // Convert local images to WebP
  const pathParts = originalSrc.split('.');
  if (pathParts.length > 1) {
    pathParts[pathParts.length - 1] = 'webp';
    return pathParts.join('.');
  }

  return originalSrc;
}

// Generate fallback placeholder based on image type
function generateFallbackSrc(originalSrc: string, alt: string): string {
  // For testimonial images, use avatar placeholder
  if (originalSrc.includes('testimonial')) {
    return '/images/avatar-placeholder.png';
  }

  // For product images, use product placeholder
  if (originalSrc.includes('product') || alt.toLowerCase().includes('genset')) {
    return "https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=Genset";
  }

  // Default placeholder
  return "https://via.placeholder.com/400x300/6B7280/FFFFFF?text=Image";
}

export function WebPImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  placeholder = 'blur',
  blurDataURL,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  fill = false,
  quality = 85,
  loading = 'lazy',
  onLoad,
  onError,
  fallbackSrc,
}: WebPImageProps) {
  // Fix loading/priority conflict
  const actualLoading = priority ? 'eager' : loading;
  const [currentSrc, setCurrentSrc] = useState(src);
  const [hasError, setHasError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Default blur placeholder
  const defaultBlurDataURL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';

  useEffect(() => {
    // Try WebP version first
    const webpSrc = getWebPUrl(src);
    setCurrentSrc(webpSrc);
    setHasError(false);
    setIsLoaded(false);
  }, [src]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    if (!hasError) {
      // First error: try original format
      if (currentSrc !== src) {
        setCurrentSrc(src);
        setHasError(false);
        return;
      }

      // Second error: use fallback
      const fallback = fallbackSrc || generateFallbackSrc(src, alt);
      setCurrentSrc(fallback);
      setHasError(true);
      onError?.();
    }
  };

  if (hasError && currentSrc === (fallbackSrc || generateFallbackSrc(src, alt))) {
    return (
      <div
        className={cn(
          'flex items-center justify-center bg-gray-200 dark:bg-gray-800 text-gray-500 dark:text-gray-400',
          className
        )}
        style={{ width, height }}
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    );
  }

  return (
    <div className={cn('relative overflow-hidden', className)} suppressHydrationWarning>
      {/* Loading skeleton */}
      {!isLoaded && (
        <div
          className={cn(
            'absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse',
            fill ? 'w-full h-full' : ''
          )}
          style={!fill ? { width, height } : undefined}
        />
      )}

      <Image
        src={currentSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        placeholder={placeholder}
        blurDataURL={blurDataURL || defaultBlurDataURL}
        sizes={sizes}
        quality={quality}
        loading={actualLoading}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0',
          fill ? 'object-cover' : ''
        )}
        style={{
          maxWidth: '100%',
          height: 'auto',
        }}
        suppressHydrationWarning
      />
    </div>
  );
}

// Specialized components for common use cases
export function TestimonialImage({
  src,
  alt,
  className,
  ...props
}: Omit<WebPImageProps, 'sizes' | 'width' | 'height'>) {
  return (
    <div suppressHydrationWarning>
      <WebPImage
        src={src}
        alt={alt}
        width={96}
        height={96}
        className={cn('rounded-full', className)}
        sizes="96px"
        quality={90}
        fallbackSrc="/images/avatar-placeholder.png"
        {...props}
      />
    </div>
  );
}

export function ProductImage({
  src,
  alt,
  className,
  ...props
}: Omit<WebPImageProps, 'sizes'>) {
  return (
    <WebPImage
      src={src}
      alt={alt}
      className={className}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
      quality={90}
      fallbackSrc="https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=Genset"
      {...props}
    />
  );
}

export function HeroImage({
  src,
  alt,
  className,
  ...props
}: Omit<WebPImageProps, 'priority' | 'sizes'>) {
  return (
    <WebPImage
      src={src}
      alt={alt}
      className={className}
      priority={true}
      sizes="100vw"
      quality={95}
      {...props}
    />
  );
}
