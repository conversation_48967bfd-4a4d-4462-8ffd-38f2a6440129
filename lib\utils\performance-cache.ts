import { LRUCache } from 'lru-cache';

// Memory cache for API responses
const apiCache = new LRUCache<string, any>({
  max: 500, // Maximum number of items
  ttl: 1000 * 60 * 5, // 5 minutes TTL
  allowStale: true,
  updateAgeOnGet: true,
  updateAgeOnHas: true,
});

// Memory cache for database queries
const dbCache = new LRUCache<string, any>({
  max: 1000,
  ttl: 1000 * 60 * 10, // 10 minutes TTL
  allowStale: true,
  updateAgeOnGet: true,
});

// Cache keys generator
export const cacheKeys = {
  products: (filters?: any) => `products:${JSON.stringify(filters || {})}`,
  product: (id: string) => `product:${id}`,
  user: (id: string) => `user:${id}`,
  rentals: (userId: string, filters?: any) => `rentals:${userId}:${JSON.stringify(filters || {})}`,
  rental: (id: string) => `rental:${id}`,
  dashboard: (userId: string) => `dashboard:${userId}`,
  adminStats: () => 'admin:stats',
  operations: (filters?: any) => `operations:${JSON.stringify(filters || {})}`,
};

// API Cache functions
export const apiCacheUtils = {
  get: <T>(key: string): T | undefined => {
    return apiCache.get(key);
  },

  set: <T>(key: string, value: T, ttl?: number): void => {
    if (ttl) {
      apiCache.set(key, value, { ttl });
    } else {
      apiCache.set(key, value);
    }
  },

  delete: (key: string): void => {
    apiCache.delete(key);
  },

  clear: (): void => {
    apiCache.clear();
  },

  has: (key: string): boolean => {
    return apiCache.has(key);
  },

  // Invalidate related cache entries
  invalidatePattern: (pattern: string): void => {
    const keys = Array.from(apiCache.keys());
    keys.forEach(key => {
      if (key.includes(pattern)) {
        apiCache.delete(key);
      }
    });
  },
};

// Database Cache functions
export const dbCacheUtils = {
  get: <T>(key: string): T | undefined => {
    return dbCache.get(key);
  },

  set: <T>(key: string, value: T, ttl?: number): void => {
    if (ttl) {
      dbCache.set(key, value, { ttl });
    } else {
      dbCache.set(key, value);
    }
  },

  delete: (key: string): void => {
    dbCache.delete(key);
  },

  clear: (): void => {
    dbCache.clear();
  },

  has: (key: string): boolean => {
    return dbCache.has(key);
  },

  invalidatePattern: (pattern: string): void => {
    const keys = Array.from(dbCache.keys());
    keys.forEach(key => {
      if (key.includes(pattern)) {
        dbCache.delete(key);
      }
    });
  },
};

// Cache decorator for functions
export function cached<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  ttl?: number,
  useDbCache = false
): T {
  const cache = useDbCache ? dbCacheUtils : apiCacheUtils;
  
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator(...args);
    
    // Try to get from cache first
    const cached = cache.get(key);
    if (cached !== undefined) {
      return cached;
    }
    
    // Execute function and cache result
    try {
      const result = await fn(...args);
      cache.set(key, result, ttl);
      return result;
    } catch (error) {
      // Don't cache errors
      throw error;
    }
  }) as T;
}

// Batch cache operations
export const batchCacheUtils = {
  getMultiple: <T>(keys: string[], useDbCache = false): (T | undefined)[] => {
    const cache = useDbCache ? dbCacheUtils : apiCacheUtils;
    return keys.map(key => cache.get<T>(key));
  },

  setMultiple: <T>(entries: Array<{ key: string; value: T; ttl?: number }>, useDbCache = false): void => {
    const cache = useDbCache ? dbCacheUtils : apiCacheUtils;
    entries.forEach(({ key, value, ttl }) => {
      cache.set(key, value, ttl);
    });
  },

  deleteMultiple: (keys: string[], useDbCache = false): void => {
    const cache = useDbCache ? dbCacheUtils : apiCacheUtils;
    keys.forEach(key => cache.delete(key));
  },
};

// Cache warming functions
export const cacheWarming = {
  warmProductsCache: async () => {
    try {
      // This would typically call your API to warm the cache
      console.log('Warming products cache...');
      // await fetch('/api/products').then(res => res.json());
    } catch (error) {
      console.error('Failed to warm products cache:', error);
    }
  },

  warmUserCache: async (userId: string) => {
    try {
      console.log(`Warming user cache for ${userId}...`);
      // await fetch(`/api/user/${userId}`).then(res => res.json());
    } catch (error) {
      console.error('Failed to warm user cache:', error);
    }
  },

  warmDashboardCache: async (userId: string) => {
    try {
      console.log(`Warming dashboard cache for ${userId}...`);
      // await fetch(`/api/dashboard/${userId}`).then(res => res.json());
    } catch (error) {
      console.error('Failed to warm dashboard cache:', error);
    }
  },
};

// Cache statistics
export const cacheStats = {
  getApiCacheStats: () => ({
    size: apiCache.size,
    max: apiCache.max,
    calculatedSize: apiCache.calculatedSize,
    hits: 0, // LRU cache doesn't track hits by default
    misses: 0,
  }),

  getDbCacheStats: () => ({
    size: dbCache.size,
    max: dbCache.max,
    calculatedSize: dbCache.calculatedSize,
    hits: 0,
    misses: 0,
  }),

  getAllStats: () => ({
    api: cacheStats.getApiCacheStats(),
    db: cacheStats.getDbCacheStats(),
  }),
};

// Cache middleware for API routes
export function withCache<T>(
  handler: (req: any, res: any) => Promise<T>,
  keyGenerator: (req: any) => string,
  ttl?: number
) {
  return async (req: any, res: any) => {
    const key = keyGenerator(req);
    
    // Check cache first
    const cached = apiCacheUtils.get(key);
    if (cached) {
      res.setHeader('X-Cache', 'HIT');
      return res.json(cached);
    }
    
    // Execute handler
    try {
      const result = await handler(req, res);
      
      // Cache successful responses
      if (res.statusCode === 200) {
        apiCacheUtils.set(key, result, ttl);
        res.setHeader('X-Cache', 'MISS');
      }
      
      return result;
    } catch (error) {
      res.setHeader('X-Cache', 'ERROR');
      throw error;
    }
  };
}

// Initialize cache system
export function initializeCache() {
  // Set up cache cleanup intervals
  setInterval(() => {
    // Force garbage collection on caches
    apiCache.purgeStale();
    dbCache.purgeStale();
  }, 1000 * 60 * 5); // Every 5 minutes

  // Log cache statistics periodically in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      console.log('Cache Stats:', cacheStats.getAllStats());
    }, 1000 * 60 * 10); // Every 10 minutes
  }
}

// Export cache instances for direct access if needed
export { apiCache, dbCache };
