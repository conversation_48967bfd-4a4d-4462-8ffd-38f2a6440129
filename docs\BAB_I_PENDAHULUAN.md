# BAB I

# PENDAHULUAN

## 1.1 Latar Belakang

Perkembangan pesat teknologi informasi dan komunikasi telah mengubah cara berbagai sektor bisnis beroperasi, termasuk industri penyewaan peralatan. Di era digital saat ini, konsumen mengharapkan kemudahan akses, transparansi informasi, dan efisiensi dalam setiap transaksi bisnis yang mereka lakukan. Namun, banyak bisnis rental genset di Indonesia masih mengandalkan sistem konvensional yang melibatkan proses manual dalam pengelolaan pemesanan, inventori, dan pembayaran.

Sistem konvensional yang masih banyak digunakan dalam bisnis rental genset memiliki berbagai keterbatasan. Proses pemesanan yang dilakukan secara langsung atau melalui telepon seringkali tidak efisien dan memakan waktu. Pelanggan kesulitan untuk mengetahui ketersediaan produk secara real-time, sementara pemilik bisnis menghadapi tantangan dalam mengelola inventori dan melacak status rental secara akurat. <PERSON><PERSON> itu, proses pembayaran yang masih manual rentan terhadap kesalahan pencatatan dan menyulitkan dalam pembuatan laporan keuangan.

Keterbatasan sistem manual juga berdampak pada aspek komunikasi antara penyedia layanan dan pelanggan. Informasi mengenai status pemesanan, konfirmasi pembayaran, dan pemberitahuan penting lainnya seringkali terlambat disampaikan, yang dapat menurunkan tingkat kepuasan pelanggan. Hal ini pada akhirnya dapat mempengaruhi reputasi bisnis dan menghambat pertumbuhan usaha.

Mengingat pentingnya efisiensi operasional dan kepuasan pelanggan dalam industri rental, diperlukan sebuah solusi teknologi yang dapat mengintegrasikan seluruh proses bisnis dalam satu platform. Sistem informasi berbasis web menjadi solusi yang tepat karena dapat diakses kapan saja dan di mana saja, memberikan informasi real-time, serta mengotomatisasi berbagai proses bisnis yang sebelumnya dilakukan secara manual.

Oleh karena itu, penelitian ini bertujuan untuk mengembangkan sistem informasi rental genset berbasis web menggunakan teknologi modern yang terintegrasi dengan payment gateway dan sistem notifikasi otomatis. Sistem ini diharapkan dapat meningkatkan efisiensi operasional, memperbaiki pengalaman pelanggan, dan memberikan kemudahan dalam pengelolaan bisnis rental genset.

## 1.2 Perumusan Masalah

Berdasarkan latar belakang yang telah diuraikan, permasalahan yang akan diselesaikan dalam penelitian ini dapat dirumuskan sebagai berikut:

1. **Pengembangan Sistem Informasi Berbasis Web**: Diperlukan pengembangan sistem informasi berbasis web menggunakan teknologi modern untuk mengelola proses pemesanan rental genset secara online yang efisien dan user-friendly.

2. **Integrasi Payment Gateway**: Diperlukan integrasi sistem pembayaran online (payment gateway) untuk memfasilitasi transaksi pembayaran deposit dan pelunasan secara aman dan mudah.

3. **Implementasi Sistem Notifikasi Otomatis**: Diperlukan implementasi sistem notifikasi otomatis melalui WhatsApp untuk memberikan informasi real-time kepada admin dan pelanggan mengenai status rental.

4. **Penyediaan Dashboard Admin**: Diperlukan penyediaan dashboard yang efektif bagi admin untuk mengelola inventori, memantau status rental, dan menghasilkan laporan bisnis yang akurat.

## 1.3 Batasan Masalah

Agar penelitian ini lebih terfokus dan terarah, maka ruang lingkup masalah dibatasi pada komponen input-proses-output sebagai berikut:

### 1.3.1 Batasan Input

1. **Data Pengguna**: Input data pengguna terbatas pada informasi dasar (nama, email, nomor telepon) untuk registrasi customer dan admin.

2. **Data Produk**: Input data produk terbatas pada genset dengan atribut nama, tipe, kapasitas, harga per hari, dan tarif overtime.

3. **Data Pemesanan**: Input pemesanan mencakup tanggal mulai, tanggal selesai, lokasi pengambilan, dan lokasi pengiriman.

4. **Data Pembayaran**: Input pembayaran melalui payment gateway Midtrans dengan sistem deposit 50% dan pelunasan 50%.

### 1.3.2 Batasan Proses

1. **Platform Teknologi**: Sistem dikembangkan menggunakan Next.js 14, PostgreSQL, dan Prisma ORM dengan arsitektur web-based responsive.

2. **Proses Bisnis**: Proses terbatas pada manajemen rental genset, tidak mencakup jenis peralatan rental lainnya.

3. **Integrasi Eksternal**: Integrasi terbatas pada Midtrans (payment gateway) dan Fonnte (WhatsApp API).

4. **Pengguna Sistem**: Sistem mendukung dua role pengguna yaitu Customer dan Admin.

5. **Lokasi Operasional**: Sistem dirancang untuk operasional dalam satu wilayah geografis, tidak multi-cabang.

### 1.3.3 Batasan Output

1. **Laporan Sistem**: Output laporan terbatas pada laporan transaksi rental dan pembayaran, tidak mencakup analisis akuntansi mendalam atau perpajakan.

2. **Notifikasi**: Output notifikasi terbatas pada WhatsApp untuk konfirmasi pesanan, status pembayaran, dan update rental.

3. **Invoice**: Output invoice dalam format PDF dengan informasi dasar rental dan pembayaran.

4. **Dashboard**: Output dashboard admin untuk monitoring inventori, status rental, dan statistik dasar.

5. **Platform Deployment**: Sistem di-deploy pada platform web hosting (Vercel) dengan database cloud (Supabase/Railway).

## 1.4 Tujuan Penelitian

Tujuan dari penelitian ini adalah sebagai berikut:

### 1.4.1 Tujuan Umum

Mengembangkan sistem informasi rental genset berbasis web menggunakan teknologi modern yang terintegrasi untuk mengotomatisasi dan meningkatkan efisiensi proses bisnis rental genset.

### 1.4.2 Tujuan Khusus

1. Mengembangkan dan mengimplementasikan sistem informasi berbasis web menggunakan teknologi modern yang dapat mengelola proses pemesanan rental genset secara online dengan antarmuka yang user-friendly dan responsif.

2. Mengintegrasikan payment gateway Midtrans untuk mempermudah dan mengamankan proses pembayaran online bagi pelanggan, termasuk sistem pembayaran deposit dan pelunasan.

3. Mengimplementasikan sistem notifikasi WhatsApp menggunakan API Fonnte untuk meningkatkan kecepatan dan efisiensi komunikasi terkait status rental antara admin dan pelanggan.

4. Menyediakan dashboard admin yang komprehensif untuk manajemen inventori genset, monitoring status rental, dan pembuatan laporan bisnis secara efektif.

5. Menghasilkan sistem pelaporan dan analitik sederhana yang dapat mendukung pengambilan keputusan bisnis berdasarkan data rental dan pembayaran.

## 1.5 Manfaat Penelitian

Hasil dari penelitian ini diharapkan dapat memberikan manfaat bagi berbagai pihak, antara lain:

### 1.5.1 Bagi Pelanggan

1. **Kemudahan Akses**: Memberikan kemudahan dalam mengakses informasi produk dan melakukan pemesanan genset kapan saja dan di mana saja melalui platform web.

2. **Transparansi Informasi**: Menyediakan informasi real-time mengenai ketersediaan genset, harga, dan status pemesanan secara transparan.

3. **Proses Pembayaran Aman**: Memfasilitasi proses pembayaran online yang aman dan mudah melalui berbagai metode pembayaran yang tersedia di Midtrans.

4. **Notifikasi Real-time**: Memberikan notifikasi otomatis melalui WhatsApp mengenai konfirmasi pesanan, status pembayaran, dan informasi penting lainnya.

### 1.5.2 Bagi Pemilik Bisnis

1. **Efisiensi Operasional**: Mengotomatisasi proses bisnis sehingga mengurangi beban kerja manual dan meningkatkan efisiensi operasional.

2. **Manajemen Inventori**: Menyediakan sistem manajemen inventori yang akurat untuk memantau ketersediaan dan status genset secara real-time.

3. **Laporan Bisnis**: Menghasilkan laporan yang akurat dan komprehensif untuk mendukung pengambilan keputusan bisnis dan perencanaan strategis.

4. **Peningkatan Layanan**: Meningkatkan kualitas layanan pelanggan melalui sistem yang responsif dan komunikasi yang efektif.

5. **Skalabilitas Bisnis**: Menyediakan platform yang dapat mendukung pertumbuhan dan ekspansi bisnis di masa depan.

### 1.5.3 Bagi Akademik

1. **Kontribusi Ilmiah**: Menjadi referensi dan kontribusi dalam pengembangan sistem informasi bisnis, khususnya dalam implementasi teknologi modern seperti payment gateway dan API notifikasi.

2. **Studi Kasus**: Memberikan studi kasus nyata tentang implementasi sistem informasi dalam industri rental yang dapat digunakan untuk penelitian selanjutnya.

3. **Pengembangan Teknologi**: Mendemonstrasikan penggunaan teknologi modern seperti Next.js, PostgreSQL, dan integrasi API dalam pengembangan aplikasi web.

## 1.6 Metodologi Penelitian

Penelitian ini menggunakan pendekatan kuantitatif dengan metode Research and Development (R&D) yang bertujuan untuk menghasilkan produk berupa sistem informasi rental genset berbasis web. Metodologi pengembangan sistem mengadopsi model System Development Life Cycle (SDLC) dengan pendekatan Waterfall.

### 1.6.1 Jenis Penelitian

Jenis penelitian yang digunakan adalah penelitian pengembangan (Research and Development) yang bersifat kuantitatif. Penelitian ini bertujuan untuk menghasilkan produk berupa sistem informasi yang dapat menyelesaikan permasalahan dalam bisnis rental genset.

### 1.6.2 Metode Pengumpulan Data

Metode pengumpulan data yang digunakan dalam penelitian ini meliputi:

**1. Observasi**

- Melakukan pengamatan langsung terhadap proses bisnis rental genset yang sedang berjalan
- Mengidentifikasi permasalahan dan kebutuhan sistem
- Menganalisis workflow dan prosedur operasional yang ada

**2. Wawancara**

- Melakukan wawancara dengan pemilik bisnis rental genset
- Wawancara dengan staff operasional untuk memahami proses bisnis
- Wawancara dengan pelanggan untuk memahami kebutuhan dan ekspektasi

**3. Studi Literatur**

- Mempelajari teori-teori yang berkaitan dengan sistem informasi rental
- Mengkaji teknologi yang akan digunakan dalam pengembangan sistem
- Mempelajari penelitian terdahulu yang relevan dengan topik penelitian

**4. Studi Dokumentasi**

- Mengumpulkan dan menganalisis dokumen-dokumen yang berkaitan dengan operasional bisnis
- Mempelajari formulir pemesanan, catatan inventori, dan data transaksi yang ada
- Menganalisis laporan keuangan dan prosedur operasional standar

### 1.6.3 Metode Pengembangan Sistem

Pengembangan sistem menggunakan metodologi SDLC dengan model Waterfall yang terdiri dari tahapan:

**1. Requirements Analysis (Analisis Kebutuhan)**

- Identifikasi kebutuhan fungsional dan non-fungsional sistem
- Analisis kebutuhan pengguna dan stakeholder
- Pembuatan Software Requirements Specification (SRS)

**2. System Design (Perancangan Sistem)**

- Perancangan arsitektur sistem secara keseluruhan
- Desain database menggunakan Entity-Relationship Diagram (ERD)
- Perancangan antarmuka pengguna (UI/UX Design)
- Pembuatan Data Flow Diagram (DFD)

**3. Implementation (Implementasi)**

- Pengembangan sistem menggunakan Next.js dan PostgreSQL
- Implementasi fitur-fitur sesuai dengan desain yang telah dibuat
- Integrasi dengan payment gateway Midtrans dan API WhatsApp Fonnte

**4. Testing (Pengujian)**

- Pengujian unit untuk setiap modul sistem
- Pengujian integrasi antar modul
- Pengujian sistem secara keseluruhan (system testing)
- User Acceptance Testing (UAT)

**5. Deployment (Penerapan)**

- Deploy sistem ke server hosting
- Konfigurasi environment produksi
- Training pengguna sistem

### 1.6.4 Alat dan Bahan Penelitian

**Perangkat Keras:**

- Laptop dengan spesifikasi minimum Intel Core i5, RAM 16GB, SSD 512GB
- Server hosting untuk deployment sistem

**Perangkat Lunak:**

- Visual Studio Code sebagai code editor
- Node.js sebagai runtime environment
- PostgreSQL sebagai database management system
- Git untuk version control

**Framework dan Library:**

- Next.js 14 untuk full-stack development
- Prisma sebagai ORM (Object-Relational Mapping)
- Tailwind CSS untuk styling
- Midtrans SDK untuk payment gateway
- Fonnte API untuk WhatsApp notifications

### 1.6.5 Teknik Analisis Data

Analisis data dilakukan secara kualitatif dan kuantitatif:

**1. Analisis Kualitatif**

- Analisis hasil observasi dan wawancara
- Identifikasi kebutuhan sistem berdasarkan data yang dikumpulkan
- Analisis gap antara sistem lama dan kebutuhan baru

**2. Analisis Kuantitatif**

- Analisis performa sistem melalui testing
- Pengukuran efisiensi sistem dibandingkan dengan sistem manual
- Analisis hasil User Acceptance Testing (UAT)

## 1.7 Sistematika Penulisan

Penulisan laporan skripsi ini disusun secara sistematis dalam lima bab dengan uraian sebagai berikut:

**BAB I PENDAHULUAN**
Bab ini berisi latar belakang masalah yang mendasari penelitian, rumusan masalah yang akan diselesaikan, batasan masalah untuk membatasi ruang lingkup penelitian, tujuan penelitian yang ingin dicapai, manfaat penelitian bagi berbagai pihak, dan sistematika penulisan laporan.

**BAB II LANDASAN TEORI**
Bab ini membahas teori-teori dan konsep-konsep yang menjadi dasar dalam perancangan dan pengembangan sistem, termasuk sistem informasi, teknologi yang digunakan, metode pengembangan perangkat lunak, dan analisis perancangan sistem.

**BAB III DESAIN SISTEM DAN PERANCANGAN PROGRAM**
Bab ini menjelaskan metodologi penelitian, tinjauan organisasi, identifikasi masalah, analisis kebutuhan, sistem lama dan alternatif, normalisasi database, ERD, DFD, kamus data, bagan berjenjang, site map, desain layout, dan desain output.

**BAB IV HASIL DAN PEMBAHASAN**
Bab ini memaparkan hasil dari analisis dan perancangan sistem, implementasi antarmuka pengguna, implementasi fitur-fitur utama sistem, serta hasil pengujian sistem yang telah dikembangkan.

**BAB V PENUTUP**
Bab ini berisi kesimpulan dari seluruh rangkaian penelitian yang telah dilakukan dan saran-saran untuk pengembangan sistem di masa yang akan datang.
