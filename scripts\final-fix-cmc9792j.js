// Final Fix Script for CMC9792J Payment Issue
// This script will inspect and fix the payment issue once and for all

async function finalFixCMC9792J() {
  console.log('🚀 FINAL FIX SCRIPT FOR CMC9792J');
  console.log('================================');
  
  try {
    // Step 1: Deep inspection
    console.log('🔍 Step 1: Deep inspection of all payments...');
    
    const inspectResponse = await fetch('/api/payments/inspect');
    const inspectData = await inspectResponse.json();
    
    if (!inspectData.success) {
      console.error('❌ Failed to inspect payments:', inspectData.error);
      console.error('Details:', inspectData.details);
      return;
    }
    
    console.log('👤 Session User:', inspectData.sessionUser);
    console.log('📊 Total Payments Found:', inspectData.totalPayments);
    
    // Show all payments
    console.log('💳 All Payments (Prisma):');
    inspectData.allPaymentsPrisma.forEach((payment, index) => {
      console.log(`  ${index + 1}. ${payment.paymentCode} - Status: ${payment.status} - Amount: ${payment.amount}`);
    });
    
    console.log('💾 All Payments (Raw SQL):');
    if (Array.isArray(inspectData.allPaymentsRaw)) {
      inspectData.allPaymentsRaw.forEach((payment, index) => {
        console.log(`  ${index + 1}. #${payment.id.substring(0, 8).toUpperCase()} - Status: ${payment.status} - Amount: ${payment.amount}`);
      });
    }
    
    // Step 2: Focus on target payment
    console.log('\n🎯 Step 2: Target Payment Analysis...');
    
    if (inspectData.targetPaymentPrisma) {
      console.log('✅ Found CMC9792J payment (Prisma):', inspectData.targetPaymentPrisma);
      console.log('🔍 Debug Info:', inspectData.debugInfo);
    } else {
      console.log('❌ CMC9792J payment not found in Prisma results');
    }
    
    if (inspectData.targetPaymentRaw) {
      console.log('✅ Found CMC9792J payment (Raw SQL):', inspectData.targetPaymentRaw);
    } else {
      console.log('❌ CMC9792J payment not found in Raw SQL results');
    }
    
    // Step 3: Determine the issue
    console.log('\n🔍 Step 3: Issue Analysis...');
    
    const targetPayment = inspectData.targetPaymentPrisma || inspectData.targetPaymentRaw;
    
    if (!targetPayment) {
      console.error('❌ CRITICAL: CMC9792J payment not found in database!');
      console.log('Available payment codes:', inspectData.allPaymentsPrisma.map(p => p.paymentCode));
      return;
    }
    
    console.log('Current Payment Status:', targetPayment.status);
    console.log('Expected UI Progress:', inspectData.debugInfo?.shouldShowProgress || 'Unknown');
    
    // Step 4: Apply fix if needed
    if (targetPayment.status === 'DEPOSIT_PENDING') {
      console.log('\n🔧 Step 4: Applying FORCE FIX...');
      
      if (confirm('❗ Payment status is DEPOSIT_PENDING. This will FORCE UPDATE it to DEPOSIT_PAID. Continue?')) {
        
        const fixResponse = await fetch('/api/payments/inspect', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'force_fix_cmc9792j',
            paymentId: 'cmc9792j'
          }),
        });
        
        const fixData = await fixResponse.json();
        
        if (fixData.success) {
          console.log('✅ FORCE FIX SUCCESSFUL!');
          console.log('Original Status:', fixData.originalStatus);
          console.log('New Status:', fixData.newStatus);
          console.log('Update Results:', fixData.updateResult, fixData.rentalUpdateResult);
          
          alert('✅ Payment has been FORCE FIXED! The page will reload to show changes.');
          
          // Force hard reload
          window.location.href = window.location.href + '?t=' + Date.now();
          
        } else {
          console.error('❌ FORCE FIX FAILED:', fixData.error);
          console.error('Details:', fixData.details);
        }
      }
    } else {
      console.log('✅ Payment status is correct:', targetPayment.status);
      console.log('\n🤔 If UI still shows 0%, the issue might be:');
      console.log('1. Frontend caching');
      console.log('2. Component state not updating');
      console.log('3. CSS/styling issue');
      
      if (confirm('Try a HARD REFRESH to clear all caches?')) {
        // Clear all possible caches
        if ('caches' in window) {
          caches.keys().then(names => {
            names.forEach(name => {
              caches.delete(name);
            });
          });
        }
        
        localStorage.clear();
        sessionStorage.clear();
        
        // Force reload with cache bypass
        window.location.reload(true);
      }
    }
    
  } catch (error) {
    console.error('❌ CRITICAL ERROR in final fix script:', error);
    console.error('Stack:', error.stack);
  }
  
  console.log('\n================================');
  console.log('🏁 Final fix script completed');
}

// Helper function to check current page data
function checkCurrentPageData() {
  console.log('🔍 Checking current page data...');
  
  // Try to find payment elements on the page
  const progressElements = document.querySelectorAll('[class*="progress"]');
  const statusElements = document.querySelectorAll('[class*="badge"], [class*="status"]');
  const paymentElements = document.querySelectorAll('[class*="payment"]');
  
  console.log('Progress elements found:', progressElements.length);
  progressElements.forEach((el, i) => {
    console.log(`  ${i + 1}.`, el.textContent, el.className);
  });
  
  console.log('Status elements found:', statusElements.length);
  statusElements.forEach((el, i) => {
    console.log(`  ${i + 1}.`, el.textContent, el.className);
  });
  
  console.log('Payment elements found:', paymentElements.length);
  paymentElements.forEach((el, i) => {
    console.log(`  ${i + 1}.`, el.textContent?.substring(0, 100), el.className);
  });
}

console.log('🚀 FINAL FIX TOOLS LOADED');
console.log('Available functions:');
console.log('- finalFixCMC9792J() - Main fix function');
console.log('- checkCurrentPageData() - Check page elements');
console.log('');
console.log('🎯 Running main fix function...');

// Auto-run the final fix
finalFixCMC9792J();
