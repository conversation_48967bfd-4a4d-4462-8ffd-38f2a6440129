import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { notFound, redirect } from "next/navigation";
import { formatDate, formatCurrency } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Check, ArrowLeft } from "lucide-react";
import { InvoiceActions } from "@/app/components/admin/invoice-actions";
import Link from "next/link";
import { getPaymentById } from "@/lib/data/payment";

export const dynamic = 'force-dynamic';

export default async function CreateInvoicePage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getSession();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil data pembayaran
  try {
    const { id: paymentId } = await params;
    const payment = await getPaymentById(paymentId);

    if (!payment || !payment.rental) {
      notFound();
    }

    // Ambil data user karena tidak disertakan dalam getPaymentById
    const user = await prisma.user.findUnique({
      where: { id: payment.rental.userId || '' },
      select: { id: true, name: true, email: true, phone: true }
    });

    if (!user) {
      console.error("User not found for payment:", payment.id);
    }

    const invoiceNumber = `INV-${payment.id.substring(0, 8)}`;
    const rental = payment.rental;
    const product = rental.product;

    return (
      <div className="container mx-auto py-6 max-w-3xl">
        <div className="mb-6">
          <Link href="/admin/payments" className="flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Kembali ke Daftar Pembayaran
          </Link>

          <h1 className="text-2xl font-bold">Invoice Deposit</h1>
          <p className="text-muted-foreground">
            Buat dan kirim invoice deposit untuk penyewaan
          </p>
        </div>

        <Card className="mb-6 border-2 border-dashed border-gray-200 p-1">
          <CardContent className="p-6 border border-gray-300">
            <div className="flex justify-between items-start mb-8">
              <div>
                <h2 className="text-2xl font-bold text-blue-700">RENTAL GENSET</h2>
                <p className="text-sm text-gray-600">Jl. Contoh No. 123, Jakarta</p>
                <p className="text-sm text-gray-600">Telp: 021-1234567</p>
                <p className="text-sm text-gray-600">Email: <EMAIL></p>
              </div>
              <div className="text-right">
                <h3 className="text-xl font-bold">INVOICE</h3>
                <p className="text-sm text-gray-600">{invoiceNumber}</p>
                <p className="text-sm text-gray-600">Tanggal: {formatDate(new Date())}</p>
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-md font-semibold border-b pb-2 mb-2">INFORMASI PELANGGAN</h3>
              <p className="font-medium">{user?.name || "Pelanggan"}</p>
              <p className="text-sm text-gray-600">{user?.email || "-"}</p>
              <p className="text-sm text-gray-600">{user?.phone || "-"}</p>
              <p className="text-sm text-gray-600">{rental.address}</p>
            </div>

            <div className="mb-8">
              <h3 className="text-md font-semibold border-b pb-2 mb-2">DETAIL PENYEWAAN</h3>
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Deskripsi</th>
                    <th className="text-right py-2">Jumlah</th>
                    <th className="text-right py-2">Harga</th>
                    <th className="text-right py-2">Total</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="py-3">
                      <p className="font-medium">{product.name} - {product.capacity} KVA</p>
                      <p className="text-gray-600">
                        Tanggal: {formatDate(rental.startDate)} - {formatDate(rental.endDate)}
                      </p>
                      <p className="text-gray-600">
                        Durasi: {rental.duration ? rental.duration.replace('_HOURS', '').replace('x8', ' x 8 Jam') : '-'}
                      </p>
                    </td>
                    <td className="text-right">1x</td>
                    <td className="text-right">{formatCurrency(product.price)}</td>
                    <td className="text-right">{formatCurrency(rental.amount)}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="flex justify-end mb-4">
              <div className="w-1/2">
                <div className="flex justify-between py-2">
                  <span className="font-medium">Total</span>
                  <span>{formatCurrency(rental.amount)}</span>
                </div>
                <div className="flex justify-between py-2 font-semibold">
                  <span>Deposit (50%)</span>
                  <span>{formatCurrency(payment.deposit)}</span>
                </div>
                <div className="flex justify-between py-2 text-gray-600">
                  <span>Sisa Pembayaran (50%)</span>
                  <span>{formatCurrency(payment.remaining)}</span>
                </div>
              </div>
            </div>

            <div className="border-t pt-4 mb-8">
              <h3 className="text-md font-semibold mb-2">CATATAN</h3>
              <p className="text-sm text-gray-600">
                Mohon melakukan pembayaran deposit sebesar {formatCurrency(payment.deposit)} sebelum tanggal
                {" "}{formatDate(rental.startDate)}. Sisa pembayaran dilakukan setelah operasi selesai.
              </p>
              <p className="text-sm text-gray-600 mt-2">
                Metode Pembayaran: Transfer Bank
              </p>
              <p className="text-sm text-gray-600">
                Bank BCA: ********** a.n. PT Rental Genset
              </p>
            </div>

            <div className="text-center text-sm text-gray-600 mt-8">
              <p>Terima kasih telah menggunakan jasa kami!</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tindakan</CardTitle>
            <CardDescription>
              Kelola invoice dan kirim ke pelanggan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2">
              <p className="text-sm text-gray-600 mb-2">
                Invoice untuk: <span className="font-medium">{user?.name || "Pelanggan"}</span>
              </p>

              <div className="flex items-center text-sm">
                <div className="h-6 w-6 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-700 mr-2">
                  <Check className="h-4 w-4" />
                </div>
                <span>Invoice telah disiapkan</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="p-6">
            <InvoiceActions
              invoiceId={payment.id}
              customerPhone={user?.phone || undefined}
              customerEmail={user?.email || undefined}
            />
          </CardFooter>
        </Card>
      </div>
    );
  } catch (error) {
    console.error("Error fetching payment:", error);
    return (
      <div className="container mx-auto py-6">
        <p className="text-red-500">Terjadi kesalahan saat memuat data pembayaran</p>
        <Link href="/admin/payments">
          <Button variant="outline" className="mt-4">
            Kembali ke Daftar Pembayaran
          </Button>
        </Link>
      </div>
    );
  }
}