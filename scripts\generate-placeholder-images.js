// Script untuk generate placeholder images dalam format WebP
const fs = require('fs');
const path = require('path');

// Create placeholder SVG for testimonials
function createTestimonialPlaceholder(name, color) {
  const initial = name.charAt(0).toUpperCase();
  return `<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
    <circle cx="48" cy="48" r="48" fill="${color}"/>
    <text x="48" y="58" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">${initial}</text>
  </svg>`;
}

// Create placeholder SVG for products
function createProductPlaceholder() {
  return `<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <rect width="400" height="300" fill="#8B5CF6"/>
    <rect x="50" y="50" width="300" height="200" rx="10" fill="white" opacity="0.2"/>
    <circle cx="200" cy="120" r="30" fill="white" opacity="0.3"/>
    <rect x="150" y="160" width="100" height="40" rx="5" fill="white" opacity="0.3"/>
    <text x="200" y="220" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">GENSET</text>
  </svg>`;
}

// Testimonial data
const testimonials = [
  { name: 'Ahmad Fadli', color: '#3B82F6' },
  { name: 'Siti Nurhayati', color: '#10B981' },
  { name: 'Budi Santoso', color: '#F59E0B' }
];

// Create images directory if it doesn't exist
const imagesDir = path.join(__dirname, '../public/images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Generate testimonial placeholders
testimonials.forEach((testimonial, index) => {
  const svg = createTestimonialPlaceholder(testimonial.name, testimonial.color);
  const filename = `testimonial-${index + 1}.svg`;
  const filepath = path.join(imagesDir, filename);
  
  fs.writeFileSync(filepath, svg);
  console.log(`✅ Created ${filename}`);
});

// Generate product placeholder
const productSvg = createProductPlaceholder();
const productFilepath = path.join(imagesDir, 'product-placeholder.svg');
fs.writeFileSync(productFilepath, productSvg);
console.log('✅ Created product-placeholder.svg');

// Create WebP conversion instructions
const instructions = `
# WebP Conversion Instructions

## For existing images:
1. Install sharp: npm install sharp
2. Run conversion script: node scripts/convert-to-webp.js

## For new images:
- Always upload in WebP format when possible
- Use online converters like squoosh.app for manual conversion
- Set quality to 85-90 for best balance of size/quality

## Generated files:
- testimonial-1.svg (Ahmad Fadli - Blue)
- testimonial-2.svg (Siti Nurhayati - Green)  
- testimonial-3.svg (Budi Santoso - Orange)
- product-placeholder.svg (Generic genset placeholder)

These SVG files are lightweight and will work as fallbacks.
`;

fs.writeFileSync(path.join(__dirname, '../WEBP-INSTRUCTIONS.md'), instructions);
console.log('✅ Created WebP conversion instructions');

console.log('\n🎉 Placeholder images generated successfully!');
console.log('📁 Location: public/images/');
console.log('📝 Instructions: WEBP-INSTRUCTIONS.md');
