import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { z } from "zod";

// Schema validasi untuk data pembayaran
const paymentSchema = z.object({
  rentalId: z.string({
    required_error: "ID penyewaan diperlukan",
  }),
  paymentMethod: z.string({
    required_error: "Metode pembayaran diperlukan",
  }),
});

export async function POST(req: NextRequest) {
  try {
    // Verifikasi autentikasi
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;

    // Parse dan validasi data
    const body = await req.json();
    const validationResult = paymentSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: "Data tidak valid" }, { status: 400 });
    }

    const { rentalId, paymentMethod } = validationResult.data;

    // Cek penyewaan
    const rental = await prisma.rental.findUnique({
      where: {
        id: rentalId,
      },
      include: {
        payment: true,
        user: true,
        product: true,
      },
    });

    if (!rental) {
      return NextResponse.json({ error: "Penyewaan tidak ditemukan" }, { status: 404 });
    }

    // Verifikasi akses
    if (rental.userId !== userId) {
      return NextResponse.json({ error: "Akses ditolak" }, { status: 403 });
    }

    // Cek apakah pembayaran ada
    if (!rental.payment) {
      return NextResponse.json({ error: "Data pembayaran tidak ditemukan" }, { status: 404 });
    }

    // Cek status pembayaran
    if (rental.payment.status === "FULLY_PAID") {
      return NextResponse.json({ error: "Pembayaran sudah selesai" }, { status: 400 });
    }

    // Hitung total yang harus dibayar (sisa 50% + biaya overtime)
    const baseAmount = rental.amount;
    const remainingAmount = baseAmount / 2; // 50% dari total

    // Hitung biaya overtime jika ada
    let overtimeAmount = 0;
    if (rental.operationalEnd && rental.operationalStart) {
      // Gunakan durasi dalam jam dari field duration
      const durationStr = rental.duration || "1x8_HOURS";
      const durationMultiplier = parseInt(durationStr.split('x')[0]) || 1;
      const bookedHours = durationMultiplier * 8; // Format Nx8_HOURS
      
      // Hitung durasi aktual dalam jam
      const actualDurationMs = rental.operationalEnd.getTime() - rental.operationalStart.getTime();
      const actualHours = Math.ceil(actualDurationMs / (60 * 60 * 1000));
      
      // Jika durasi aktual lebih dari yang dipesan, ada overtime
      if (actualHours > bookedHours) {
        const overtimeHours = actualHours - bookedHours;
        
        // Hitung harga per jam dari harga produk
        const hourlyRate = rental.product.price / 8; // Asumsikan harga per 8 jam
        // Tarif overtime: 1.5x dari harga normal per jam
        overtimeAmount = overtimeHours * hourlyRate * 1.5;
      }
    }

    // Total yang harus dibayar
    const totalAmount = remainingAmount + overtimeAmount;

    // Update status pembayaran
    await prisma.payment.update({
      where: {
        id: rental.payment.id,
      },
      data: {
        status: "FULLY_PAID",
        overtimeCost: overtimeAmount > 0 ? overtimeAmount : null,
        updatedAt: new Date(),
      },
    });

    // Update status penyewaan jika operasi sudah selesai
    if (rental.operationalEnd) {
      await prisma.rental.update({
        where: {
          id: rentalId,
        },
        data: {
          status: "COMPLETED",

        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Pembayaran berhasil diproses",
      data: {
        rentalId,
        remainingAmount,
        overtimeAmount,
        totalAmount,
        paymentMethod,
      },
    });
  } catch (error) {
    console.error("[PAYMENT_PROCESS]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
} 
