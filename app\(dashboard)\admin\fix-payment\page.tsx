"use client";

import { useState } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";

export default function FixPaymentPage() {
  const [paymentCode, setPaymentCode] = useState("");
  const [paymentInfo, setPaymentInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const checkPayment = async () => {
    if (!paymentCode.trim()) {
      setMessage("Please enter a payment code");
      return;
    }

    setLoading(true);
    setMessage("");

    try {
      const response = await fetch(`/api/payments/fix-status?code=${encodeURIComponent(paymentCode)}`);
      const data = await response.json();

      if (data.success) {
        setPaymentInfo(data.payment);
        setMessage("");
      } else {
        setMessage(data.error || "Payment not found");
        setPaymentInfo(null);
      }
    } catch (error) {
      setMessage("Error checking payment");
      setPaymentInfo(null);
    } finally {
      setLoading(false);
    }
  };

  const fixPaymentStatus = async () => {
    if (!paymentCode.trim()) {
      setMessage("Please enter a payment code");
      return;
    }

    setLoading(true);
    setMessage("");

    try {
      const response = await fetch('/api/payments/fix-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentCode }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage("✅ Payment status updated successfully!");
        // Refresh payment info
        await checkPayment();
      } else {
        setMessage(data.message || data.error || "Failed to update payment status");
      }
    } catch (error) {
      setMessage("Error updating payment status");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Fix Payment Status</CardTitle>
          <p className="text-sm text-gray-600">
            Use this tool to fix payment status issues. Enter the payment code (e.g., CMC9792J) to check and fix the status.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Enter payment code (e.g., CMC9792J)"
              value={paymentCode}
              onChange={(e) => setPaymentCode(e.target.value)}
              className="flex-1"
            />
            <Button onClick={checkPayment} disabled={loading}>
              {loading ? "Checking..." : "Check"}
            </Button>
          </div>

          {message && (
            <div className={`p-3 rounded-md ${
              message.includes("✅") ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
            }`}>
              {message}
            </div>
          )}

          {paymentInfo && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Payment Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Payment ID</label>
                    <p className="font-mono text-sm">{paymentInfo.id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <div>
                      <Badge variant={
                        paymentInfo.status === "FULLY_PAID" ? "default" :
                        paymentInfo.status === "DEPOSIT_PAID" ? "secondary" :
                        "destructive"
                      }>
                        {paymentInfo.status}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Amount</label>
                    <p>Rp {paymentInfo.amount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Deposit</label>
                    <p>Rp {paymentInfo.deposit.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Remaining</label>
                    <p>Rp {paymentInfo.remaining.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Rental Status</label>
                    <Badge variant="outline">{paymentInfo.rental.status}</Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Customer</label>
                  <p>{paymentInfo.rental.user.name} ({paymentInfo.rental.user.email})</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Product</label>
                  <p>{paymentInfo.rental.product.name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Transaction ID</label>
                  <p className="font-mono text-sm">{paymentInfo.transactionId || "N/A"}</p>
                </div>

                {paymentInfo.status === "DEPOSIT_PENDING" && (
                  <div className="pt-4 border-t">
                    <Button onClick={fixPaymentStatus} disabled={loading} className="w-full">
                      {loading ? "Updating..." : "Fix Payment Status (Mark as DEPOSIT_PAID)"}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
