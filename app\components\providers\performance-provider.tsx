'use client';

import { useEffect } from 'react';
import { initializePerformanceMonitoring, performanceMonitor } from '@/lib/utils/performance-monitor';

export function PerformanceProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    // Initialize performance monitoring
    initializePerformanceMonitoring();

    // Register Service Worker - only in browser
    if (typeof navigator !== 'undefined' && 'serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);

          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content available, refresh page
                  if (confirm('Pembaruan tersedia. Muat ulang halaman?')) {
                    window.location.reload();
                  }
                }
              });
            }
          });
        })
        .catch((error) => {
          console.log('SW registration failed: ', error);
        });
    }

    // Prefetch critical resources
    const prefetchResources = [
      '/api/products',
      '/api/user/profile',
      '/_next/static/css/',
    ];

    prefetchResources.forEach((url) => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });

    // Preload critical fonts
    const preloadFonts = [
      'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
    ];

    preloadFonts.forEach((url) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      link.as = 'style';
      link.onload = function () {
        // @ts-ignore
        this.onload = null;
        // @ts-ignore
        this.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    });

    // Optimize images with Intersection Observer
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    });

    // Observe all images with data-src
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach((img) => imageObserver.observe(img));

    // Performance monitoring
    if ('performance' in window) {
      // Monitor Core Web Vitals
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log);
        getFID(console.log);
        getFCP(console.log);
        getLCP(console.log);
        getTTFB(console.log);
      });

      // Monitor resource loading
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 1000) {
            console.warn('Slow resource:', entry.name, entry.duration);
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
    }

    // Cleanup function
    return () => {
      imageObserver.disconnect();
    };
  }, []);

  return <>{children}</>;
}

// Hook for prefetching pages
export function usePrefetch() {
  const prefetchPage = (href: string) => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = href;
        document.head.appendChild(link);
      });
    } else {
      setTimeout(() => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = href;
        document.head.appendChild(link);
      }, 100);
    }
  };

  const preloadPage = (href: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'document';
    document.head.appendChild(link);
  };

  return { prefetchPage, preloadPage };
}

// Component for critical resource hints
export function ResourceHints() {
  return (
    <>
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//api.sandbox.midtrans.com" />
      <link rel="dns-prefetch" href="//app.sandbox.midtrans.com" />

      {/* Preconnect to critical origins */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

      {/* Preload critical resources */}
      <link
        rel="preload"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        as="style"
        onLoad="this.onload=null;this.rel='stylesheet'"
      />

      {/* Module preload for critical chunks */}
      <link rel="modulepreload" href="/_next/static/chunks/main.js" />
      <link rel="modulepreload" href="/_next/static/chunks/webpack.js" />

      {/* Prefetch likely navigation targets */}
      <link rel="prefetch" href="/user/rentals" />
      <link rel="prefetch" href="/user/products" />
      <link rel="prefetch" href="/api/products" />
    </>
  );
}
