import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/server';
import { prisma } from '@/lib/config/prisma';
import { apiCacheUtils, cacheKeys } from '@/lib/utils/performance-cache';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const cacheKey = cacheKeys.user(userId);
    
    // Try to get from cache first
    const cached = apiCacheUtils.get(cacheKey);
    if (cached) {
      const response = NextResponse.json(cached);
      response.headers.set('X-Cache', 'HIT');
      response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=600');
      return response;
    }

    // Get user profile from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        whatsappNumber: true,
        createdAt: true,
        _count: {
          select: {
            rentals: true,
            products: true,
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Format response
    const profileData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      whatsappNumber: user.whatsappNumber,
      createdAt: user.createdAt,
      stats: {
        totalRentals: user._count.rentals,
        totalProducts: user._count.products,
      }
    };

    // Cache for 5 minutes
    apiCacheUtils.set(cacheKey, profileData, 1000 * 60 * 5);

    const response = NextResponse.json(profileData);
    response.headers.set('X-Cache', 'MISS');
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=600');
    
    return response;

  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const body = await request.json();
    
    // Validate input
    const { name, whatsappNumber } = body;
    
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Update user profile
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name: name.trim(),
        whatsappNumber: whatsappNumber?.trim() || null,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        whatsappNumber: true,
        createdAt: true,
        _count: {
          select: {
            rentals: true,
            products: true,
          }
        }
      }
    });

    // Format response
    const profileData = {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      whatsappNumber: updatedUser.whatsappNumber,
      createdAt: updatedUser.createdAt,
      stats: {
        totalRentals: updatedUser._count.rentals,
        totalProducts: updatedUser._count.products,
      }
    };

    // Invalidate cache
    const cacheKey = cacheKeys.user(userId);
    apiCacheUtils.delete(cacheKey);

    return NextResponse.json({
      message: 'Profile updated successfully',
      user: profileData
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
