{"name": "RentalGenset - <PERSON><PERSON><PERSON> Genset Terpercaya", "short_name": "RentalGenset", "description": "Platform rental genset modern dengan layanan profesional untuk berbagai kebutuhan listrik industri dan komersial", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#8b5cf6", "orientation": "portrait-primary", "scope": "/", "lang": "id", "dir": "ltr", "categories": ["business", "utilities"], "icons": [{"src": "/images/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/images/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "<PERSON><PERSON>", "short_name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> proses penye<PERSON> genset", "url": "/user/catalog", "icons": [{"src": "/images/shortcut-rent.png", "sizes": "96x96"}]}, {"name": "Riwayat Sewa", "short_name": "Riwayat", "description": "Lihat riwayat penyewaan", "url": "/user/rentals", "icons": [{"src": "/images/shortcut-history.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/images/screenshot-wide.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Dashboard RentalGenset"}, {"src": "/images/screenshot-narrow.png", "sizes": "750x1334", "type": "image/png", "form_factor": "narrow", "label": "Mobile RentalGenset"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}