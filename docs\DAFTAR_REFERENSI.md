# DAFTAR REFEREN<PERSON>, <PERSON>, & <PERSON>, <PERSON> (2021). _Introducing React Server Components_. React Blog. Retrieved from https://react.dev/blog/2020/12/21/data-fetching-with-react-server-components

<PERSON>, <PERSON><PERSON>, & <PERSON>, G. <PERSON> (2012). Access-based consumption: The case of car sharing. _Journal of Consumer Research_, 39(4), 881-898.

Better Auth. (2024). _Better Auth Documentation_. Retrieved from https://www.better-auth.com/docs

<PERSON>, <PERSON>, <PERSON>, J<PERSON>, & <PERSON>, I. (2005). _The Unified Modeling Language User Guide_ (2nd ed.). Addison-<PERSON>.

<PERSON>, S. (2018). _Building Microservices: Designing Fine-Grained Systems_ (2nd ed.). O'Reilly Media.

Chen, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2022). Modern object-relational mapping frameworks: A comprehensive survey. _ACM Computing Surveys_, 54(8), 1-35.

<PERSON>, <PERSON> (1976). The entity-relationship model—toward a unified view of data. _ACM Transactions on Database Systems_, 1(1), 9-36.

<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> (2019). A mixed method investigation of sharing economy driven car-hailing services: Online and offline perspectives. _International Journal of Information Management_, 41, 57-64.

<PERSON>hlberg, T., <PERSON>, J., & Ondrus, J. (2018). A critical review of mobile payment research. _Electronic Commerce Research and Applications_, 14(5), 265-284.

Deitel, P., & Deitel, H. (2012). _Internet & World Wide Web How to Program_ (5th ed.). Prentice Hall.

Duckett, J. (2014). _JavaScript and JQuery: Interactive Front-End Web Development_. Wiley.

Fielding, R. T. (2000). _Architectural styles and the design of network-based software architectures_ (Doctoral dissertation). University of California, Irvine.

Fonnte. (2024). _Fonnte API Documentation_. Retrieved from https://docs.fonnte.com

Fowler, M. (2003). _Patterns of Enterprise Application Architecture_. Addison-Wesley.

Fowler, M. (2004). _UML Distilled: A Brief Guide to the Standard Object Modeling Language_ (3rd ed.). Addison-Wesley.

Gao, M., Liu, K., & Wu, Z. (2021). Empirical study of modern JavaScript frameworks for web application development. _IEEE Transactions on Software Engineering_, 47(8), 1690-1705.

Gassner, D. (2020). _Full Stack Development with Spring Boot and React_ (3rd ed.). Packt Publishing.

Heizer, J., & Render, B. (2014). _Operations Management: Sustainability and Supply Chain Management_ (11th ed.). Pearson.

Kotler, P., & Armstrong, G. (2018). _Principles of Marketing_ (17th ed.). Pearson.

Kumar, V., & Reinartz, W. (2018). _Customer Relationship Management: Concept, Strategy, and Tools_ (3rd ed.). Springer.

Laudon, K. C., & Laudon, J. P. (2014). _Management Information Systems: Managing the Digital Firm_ (13th ed.). Pearson.

Laudon, K. C., & Traver, C. G. (2020). _E-commerce 2020: Business, Technology and Society_ (16th ed.). Pearson.

McLeod, R., & Schell, G. (2007). _Management Information Systems_ (10th ed.). Prentice Hall.

Midtrans. (2024). _Midtrans Payment Gateway Documentation_. Retrieved from https://docs.midtrans.com

Next.js. (2024). _Next.js Documentation_. Retrieved from https://nextjs.org/docs

O'Brien, J. A., & Marakas, G. M. (2011). _Management Information Systems_ (10th ed.). McGraw-Hill.

Obe, R., & Hsu, L. (2017). _PostgreSQL: Up and Running_ (3rd ed.). O'Reilly Media.

Osmani, A. (2017). _Learning JavaScript Design Patterns_ (2nd ed.). O'Reilly Media.

Pavlo, A., Angulo, G., Arulraj, J., Lin, H., Lin, J., Ma, L., ... & Zhang, T. (2017). Self-driving database management systems. _VLDB Journal_, 26(4), 557-577.

Pinedo, M. L. (2016). _Scheduling: Theory, Algorithms, and Systems_ (5th ed.). Springer.

Pressman, R. S. (2014). _Software Engineering: A Practitioner's Approach_ (8th ed.). McGraw-Hill.

Prisma. (2024). _Prisma Documentation_. Retrieved from https://www.prisma.io/docs

React. (2024). _React Documentation_. Retrieved from https://react.dev/learn

Richardson, L., & Ruby, S. (2007). _RESTful Web Services_. O'Reilly Media.

Romney, M. B., & Steinbart, P. J. (2015). _Accounting Information Systems_ (13th ed.). Pearson.

Royce, W. W. (1970). Managing the development of large software systems. _Proceedings of IEEE WESCON_, 26, 1-9.

shadcn/ui. (2024). _shadcn/ui Component Library Documentation_. Retrieved from https://ui.shadcn.com

Sommerville, I. (2016). _Software Engineering_ (10th ed.). Pearson.

Stair, R., & Reynolds, G. (2018). _Principles of Information Systems_ (13th ed.). Cengage Learning.

Stevenson, W. J. (2018). _Operations Management_ (13th ed.). McGraw-Hill.

Sunyoto, D. (2013). _Metodologi Penelitian Akuntansi_. Refika Aditama.

Sutabri, T. (2012). _Konsep Sistem Informasi_. Andi Offset.

Tailwind CSS. (2024). _Tailwind CSS Documentation_. Retrieved from https://tailwindcss.com/docs

Tanenbaum, A. S., & Van Steen, M. (2017). _Distributed Systems: Principles and Paradigms_ (3rd ed.). Pearson.

Turban, E., Outland, J., King, D., Lee, J. K., Liang, T. P., & Turban, D. C. (2018). _Electronic Commerce 2018: A Managerial and Social Networks Perspective_ (9th ed.). Springer.

TypeScript. (2024). _TypeScript Documentation_. Retrieved from https://www.typescriptlang.org/docs

Vercel. (2024). _Vercel Platform Documentation_. Retrieved from https://vercel.com/docs

Whitten, J. L., & Bentley, L. D. (2007). _Systems Analysis and Design Methods_ (7th ed.). McGraw-Hill.

Yourdon, E., & Constantine, L. L. (1979). _Structured Design: Fundamentals of a Discipline of Computer Program and Systems Design_. Prentice Hall.

Zod. (2024). _Zod Schema Validation Documentation_. Retrieved from https://zod.dev
