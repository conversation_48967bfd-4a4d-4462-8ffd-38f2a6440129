import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";

export async function GET() {
  try {
    const session = await getSession();
    
    // Only allow admin users to access this debug endpoint
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all payments with their rental information
    const payments = await prisma.payment.findMany({
      include: {
        rental: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10 // Limit to last 10 payments
    });

    return NextResponse.json({
      success: true,
      payments: payments.map(payment => ({
        id: payment.id,
        rentalId: payment.rentalId,
        status: payment.status,
        amount: payment.amount,
        deposit: payment.deposit,
        remaining: payment.remaining,
        overtimeCost: payment.overtimeCost,
        transactionId: payment.transactionId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        rental: {
          id: payment.rental.id,
          status: payment.rental.status,
          user: payment.rental.user,
          product: payment.rental.product
        }
      }))
    });
  } catch (error) {
    console.error("[PAYMENT_DEBUG]", error);
    return NextResponse.json(
      { error: "Failed to fetch payment debug info" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getSession();
    
    // Only allow admin users to access this debug endpoint
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { action, paymentId, newStatus } = await request.json();

    if (action === "update_status" && paymentId && newStatus) {
      // Update payment status manually for testing
      const payment = await prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: newStatus,
          updatedAt: new Date()
        },
        include: {
          rental: true
        }
      });

      // Update rental status if needed
      if (newStatus === "DEPOSIT_PAID") {
        await prisma.rental.update({
          where: { id: payment.rentalId },
          data: {
            status: "ACTIVE"
          }
        });
      } else if (newStatus === "FULLY_PAID") {
        await prisma.rental.update({
          where: { id: payment.rentalId },
          data: {
            status: "COMPLETED"
          }
        });
      }

      return NextResponse.json({
        success: true,
        message: `Payment status updated to ${newStatus}`,
        payment
      });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("[PAYMENT_DEBUG_UPDATE]", error);
    return NextResponse.json(
      { error: "Failed to update payment status" },
      { status: 500 }
    );
  }
}
