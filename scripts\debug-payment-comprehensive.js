// Comprehensive Payment Debug Script
// Run this in browser console while logged in

async function debugPaymentComprehensive() {
  console.log('🔍 Starting comprehensive payment debug...');
  console.log('=====================================');
  
  try {
    // Step 1: Get detailed payment information
    console.log('📋 Step 1: Fetching detailed payment information...');
    const debugResponse = await fetch('/api/payments/debug-detailed');
    const debugData = await debugResponse.json();
    
    if (!debugData.success) {
      console.error('❌ Failed to get payment debug info:', debugData.error);
      return;
    }
    
    console.log('👤 Session User:', debugData.sessionUser);
    console.log('📊 Total Payments:', debugData.totalPayments);
    console.log('💳 All Payments:', debugData.payments);
    
    // Step 2: Find the CMC9792J payment
    const targetPayment = debugData.payments.find(p => p.paymentCode === '#CMC9792J');
    
    if (!targetPayment) {
      console.error('❌ Payment #CMC9792J not found in user payments');
      console.log('Available payment codes:', debugData.payments.map(p => p.paymentCode));
      return;
    }
    
    console.log('🎯 Found target payment:', targetPayment);
    console.log('=====================================');
    
    // Step 3: Analyze the payment status
    console.log('🔍 Step 3: Analyzing payment status...');
    console.log('Current Status:', targetPayment.status);
    console.log('Expected Progress:', targetPayment.uiCalculations.progressPercentage + '%');
    console.log('Should Show Lunasi Button:', targetPayment.uiCalculations.shouldShowLunasiButton);
    console.log('Status Badge:', targetPayment.uiCalculations.statusBadge);
    
    // Step 4: Check if status needs fixing
    if (targetPayment.status === 'DEPOSIT_PENDING') {
      console.log('⚠️ Payment status is DEPOSIT_PENDING - needs fixing!');
      
      if (confirm('Payment status is DEPOSIT_PENDING but should be DEPOSIT_PAID. Do you want to fix it?')) {
        console.log('🔧 Step 4: Fixing payment status...');
        
        const fixResponse = await fetch('/api/payments/debug-detailed', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'force_update',
            paymentId: targetPayment.id,
            newStatus: 'DEPOSIT_PAID'
          }),
        });
        
        const fixData = await fixResponse.json();
        
        if (fixData.success) {
          console.log('✅ Payment status fixed successfully!');
          console.log('Updated payment:', fixData.payment);
          
          // Step 5: Verify the fix
          console.log('🔄 Step 5: Verifying the fix...');
          
          // Wait a moment for database to update
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Fetch updated data
          const verifyResponse = await fetch('/api/payments/debug-detailed');
          const verifyData = await verifyResponse.json();
          
          const updatedPayment = verifyData.payments.find(p => p.paymentCode === '#CMC9792J');
          
          if (updatedPayment && updatedPayment.status === 'DEPOSIT_PAID') {
            console.log('✅ Verification successful! Payment status is now:', updatedPayment.status);
            console.log('📊 Progress should now be:', updatedPayment.uiCalculations.progressPercentage + '%');
            
            if (confirm('Payment has been fixed! Would you like to reload the page to see the changes?')) {
              window.location.reload();
            }
          } else {
            console.error('❌ Verification failed. Payment status is still:', updatedPayment?.status);
          }
        } else {
          console.error('❌ Failed to fix payment status:', fixData.error);
        }
      }
    } else {
      console.log('✅ Payment status is already correct:', targetPayment.status);
      console.log('🤔 If the UI is still showing 0%, there might be a frontend caching issue.');
      
      if (confirm('Payment status looks correct in database. Try refreshing the page?')) {
        window.location.reload();
      }
    }
    
  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
  
  console.log('=====================================');
  console.log('🏁 Debug script completed');
}

// Helper function to clear browser cache
function clearBrowserCache() {
  console.log('🧹 Clearing browser cache...');
  
  // Clear localStorage
  localStorage.clear();
  
  // Clear sessionStorage
  sessionStorage.clear();
  
  // Force reload without cache
  window.location.reload(true);
}

// Helper function to check network requests
function monitorNetworkRequests() {
  console.log('🌐 Monitoring network requests...');
  
  // Override fetch to log requests
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    console.log('🌐 Fetch request:', args[0]);
    return originalFetch.apply(this, args)
      .then(response => {
        console.log('📥 Fetch response:', args[0], response.status);
        return response;
      });
  };
  
  console.log('✅ Network monitoring enabled. Check console for fetch requests.');
}

console.log('🚀 Payment Debug Tools Loaded!');
console.log('Available functions:');
console.log('- debugPaymentComprehensive() - Main debug function');
console.log('- clearBrowserCache() - Clear cache and reload');
console.log('- monitorNetworkRequests() - Monitor API calls');
console.log('');
console.log('Run: debugPaymentComprehensive()');

// Auto-run the debug
debugPaymentComprehensive();
