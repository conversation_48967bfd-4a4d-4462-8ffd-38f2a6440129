'use client';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, any> = new Map();
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Initialize performance monitoring
  init() {
    if (typeof window === 'undefined') return;

    this.setupCoreWebVitals();
    this.setupResourceMonitoring();
    this.setupNavigationMonitoring();
    this.setupUserInteractionMonitoring();
  }

  // Core Web Vitals monitoring
  private setupCoreWebVitals() {
    if ('web-vitals' in window || typeof window === 'undefined') return;

    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS((metric) => {
        this.recordMetric('CLS', metric);
        this.reportToAnalytics('CLS', metric.value);
      });

      getFID((metric) => {
        this.recordMetric('FID', metric);
        this.reportToAnalytics('FID', metric.value);
      });

      getFCP((metric) => {
        this.recordMetric('FCP', metric);
        this.reportToAnalytics('FCP', metric.value);
      });

      getLCP((metric) => {
        this.recordMetric('LCP', metric);
        this.reportToAnalytics('LCP', metric.value);
      });

      getTTFB((metric) => {
        this.recordMetric('TTFB', metric);
        this.reportToAnalytics('TTFB', metric.value);
      });
    });
  }

  // Resource loading monitoring
  private setupResourceMonitoring() {
    if (!('PerformanceObserver' in window)) return;

    const resourceObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        const resource = entry as PerformanceResourceTiming;
        
        // Monitor slow resources
        if (resource.duration > 1000) {
          this.recordMetric('slow-resource', {
            name: resource.name,
            duration: resource.duration,
            size: resource.transferSize,
            type: this.getResourceType(resource.name),
          });
        }

        // Monitor failed resources
        if (resource.transferSize === 0 && resource.duration > 0) {
          this.recordMetric('failed-resource', {
            name: resource.name,
            duration: resource.duration,
          });
        }
      });
    });

    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);
  }

  // Navigation monitoring
  private setupNavigationMonitoring() {
    if (!('PerformanceObserver' in window)) return;

    const navigationObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        const nav = entry as PerformanceNavigationTiming;
        
        this.recordMetric('navigation', {
          domContentLoaded: nav.domContentLoadedEventEnd - nav.domContentLoadedEventStart,
          loadComplete: nav.loadEventEnd - nav.loadEventStart,
          domInteractive: nav.domInteractive - nav.navigationStart,
          firstPaint: nav.responseEnd - nav.requestStart,
        });
      });
    });

    navigationObserver.observe({ entryTypes: ['navigation'] });
    this.observers.push(navigationObserver);
  }

  // User interaction monitoring
  private setupUserInteractionMonitoring() {
    // Monitor click responsiveness
    document.addEventListener('click', (event) => {
      const startTime = performance.now();
      
      requestAnimationFrame(() => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        if (duration > 100) {
          this.recordMetric('slow-interaction', {
            type: 'click',
            target: (event.target as Element)?.tagName,
            duration,
          });
        }
      });
    });

    // Monitor scroll performance
    let scrollStartTime = 0;
    document.addEventListener('scroll', () => {
      if (scrollStartTime === 0) {
        scrollStartTime = performance.now();
      }
    });

    document.addEventListener('scrollend', () => {
      if (scrollStartTime > 0) {
        const duration = performance.now() - scrollStartTime;
        this.recordMetric('scroll-performance', { duration });
        scrollStartTime = 0;
      }
    });
  }

  // Record metric
  private recordMetric(name: string, data: any) {
    const timestamp = Date.now();
    const existing = this.metrics.get(name) || [];
    existing.push({ ...data, timestamp });
    
    // Keep only last 100 entries per metric
    if (existing.length > 100) {
      existing.shift();
    }
    
    this.metrics.set(name, existing);
  }

  // Get resource type from URL
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|webp|svg)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|otf)$/)) return 'font';
    return 'other';
  }

  // Report to analytics
  private reportToAnalytics(metric: string, value: number) {
    // In production, you would send this to your analytics service
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance Metric - ${metric}:`, value);
    }

    // Example: Send to Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', metric, {
        event_category: 'Web Vitals',
        value: Math.round(value),
        non_interaction: true,
      });
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    const summary: any = {};
    
    this.metrics.forEach((values, key) => {
      if (values.length > 0) {
        const latest = values[values.length - 1];
        const average = values.reduce((sum: number, item: any) => {
          const value = typeof item === 'object' ? item.duration || item.value || 0 : item;
          return sum + value;
        }, 0) / values.length;
        
        summary[key] = {
          latest,
          average: Math.round(average),
          count: values.length,
        };
      }
    });
    
    return summary;
  }

  // Get performance score
  getPerformanceScore(): number {
    const summary = this.getPerformanceSummary();
    let score = 100;
    
    // Deduct points based on Core Web Vitals
    if (summary.LCP?.latest?.value > 2500) score -= 20;
    else if (summary.LCP?.latest?.value > 4000) score -= 40;
    
    if (summary.FID?.latest?.value > 100) score -= 20;
    else if (summary.FID?.latest?.value > 300) score -= 40;
    
    if (summary.CLS?.latest?.value > 0.1) score -= 20;
    else if (summary.CLS?.latest?.value > 0.25) score -= 40;
    
    // Deduct points for slow resources
    if (summary['slow-resource']?.count > 5) score -= 10;
    if (summary['failed-resource']?.count > 0) score -= 15;
    
    return Math.max(0, score);
  }

  // Clear metrics
  clearMetrics() {
    this.metrics.clear();
  }

  // Cleanup observers
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Performance testing utilities
export const performanceTesting = {
  // Measure function execution time
  measureFunction: async <T>(fn: () => Promise<T> | T, name?: string): Promise<{ result: T; duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    if (name) {
      console.log(`${name} took ${duration.toFixed(2)}ms`);
    }
    
    return { result, duration };
  },

  // Measure component render time
  measureRender: (componentName: string) => {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      console.log(`${componentName} render took ${duration.toFixed(2)}ms`);
    };
  },

  // Lighthouse audit simulation
  simulateLighthouseAudit: () => {
    const monitor = PerformanceMonitor.getInstance();
    const summary = monitor.getPerformanceSummary();
    
    const audit = {
      performance: monitor.getPerformanceScore(),
      metrics: {
        'First Contentful Paint': summary.FCP?.latest?.value || 0,
        'Largest Contentful Paint': summary.LCP?.latest?.value || 0,
        'First Input Delay': summary.FID?.latest?.value || 0,
        'Cumulative Layout Shift': summary.CLS?.latest?.value || 0,
        'Time to First Byte': summary.TTFB?.latest?.value || 0,
      },
      opportunities: [],
      diagnostics: [],
    };
    
    // Add opportunities based on metrics
    if (summary.LCP?.latest?.value > 2500) {
      audit.opportunities.push('Optimize Largest Contentful Paint');
    }
    
    if (summary['slow-resource']?.count > 0) {
      audit.opportunities.push('Optimize slow loading resources');
    }
    
    if (summary.CLS?.latest?.value > 0.1) {
      audit.opportunities.push('Reduce Cumulative Layout Shift');
    }
    
    return audit;
  },
};

// Initialize performance monitoring
export function initializePerformanceMonitoring() {
  if (typeof window !== 'undefined') {
    const monitor = PerformanceMonitor.getInstance();
    monitor.init();
    
    // Report performance summary every 30 seconds in development
    if (process.env.NODE_ENV === 'development') {
      setInterval(() => {
        const summary = monitor.getPerformanceSummary();
        const score = monitor.getPerformanceScore();
        console.log('Performance Summary:', { score, summary });
      }, 30000);
    }
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();
