import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { PaymentStatus } from "@prisma/client";

export async function POST(request: Request) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { orderId, transactionStatus, transactionId } = await request.json();

    console.log('[MANUAL_CALLBACK] Processing manual callback:', { 
      orderId, 
      transactionStatus, 
      transactionId,
      userId: session.user.id 
    });

    if (!orderId) {
      return NextResponse.json({ error: "Order ID is required" }, { status: 400 });
    }

    // Parse the order ID to extract rental ID and payment type
    // Format: {rentalId}_{paymentType}_{timestamp}
    const orderParts = orderId.split('_');
    if (orderParts.length < 2) {
      return NextResponse.json({ error: "Invalid order ID format" }, { status: 400 });
    }

    const rentalId = orderParts[0];
    const paymentType = orderParts[1]; // 'deposit', 'remaining', or 'full'

    console.log('[MANUAL_CALLBACK] Parsed data:', { rentalId, paymentType });

    // Verify that this rental belongs to the current user (or user is admin)
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: {
        user: true,
        product: true
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Rental not found" }, { status: 404 });
    }

    if (session.user.role !== "ADMIN" && rental.userId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Process successful payment
    if (transactionStatus === 'settlement') {
      console.log('[MANUAL_CALLBACK] Payment successful, updating status...');
      
      // Determine new payment status
      let newStatus: PaymentStatus = PaymentStatus.DEPOSIT_PENDING;
      if (paymentType === 'deposit') {
        newStatus = PaymentStatus.DEPOSIT_PAID;
      } else if (paymentType === 'remaining') {
        newStatus = PaymentStatus.FULLY_PAID;
      } else if (paymentType === 'full') {
        newStatus = PaymentStatus.FULLY_PAID;
      }

      // Update payment status in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Update payment status
        const payment = await tx.payment.update({
          where: { rentalId },
          data: {
            status: newStatus,
            transactionId: transactionId || orderId,
            updatedAt: new Date()
          },
          include: {
            rental: {
              include: {
                user: {
                  select: {
                    name: true,
                    phone: true,
                    email: true
                  }
                },
                product: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        });

        // Update rental status
        let rentalStatus = "PENDING";
        if (newStatus === PaymentStatus.DEPOSIT_PAID) {
          rentalStatus = "ACTIVE";
        } else if (newStatus === PaymentStatus.FULLY_PAID) {
          rentalStatus = "COMPLETED";
        }

        await tx.rental.update({
          where: { id: rentalId },
          data: {
            status: rentalStatus,
            updatedAt: new Date()
          }
        });

        return { payment, rentalStatus };
      });

      console.log(`[MANUAL_CALLBACK] Payment status updated: ${rentalId} -> ${newStatus}`);
      console.log(`[MANUAL_CALLBACK] Rental status updated: ${rentalId} -> ${result.rentalStatus}`);

      return NextResponse.json({
        success: true,
        message: "Payment callback processed successfully",
        data: {
          rentalId,
          paymentType,
          oldPaymentStatus: "DEPOSIT_PENDING", // We assume it was pending
          newPaymentStatus: newStatus,
          rentalStatus: result.rentalStatus,
          transactionId: transactionId || orderId
        }
      });

    } else {
      return NextResponse.json({
        success: false,
        message: "Payment not successful",
        transactionStatus
      });
    }

  } catch (error) {
    console.error("[MANUAL_CALLBACK_ERROR]", error);
    return NextResponse.json(
      { 
        error: "Failed to process manual callback", 
        details: error.message 
      },
      { status: 500 }
    );
  }
}
