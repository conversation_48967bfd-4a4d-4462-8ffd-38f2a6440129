// Script untuk generate logo yang diperlukan
const fs = require('fs');
const path = require('path');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.log('❌ Sharp not found. Run: npm install sharp');
  process.exit(1);
}

const imagesDir = path.join(__dirname, '../public/images');

// Create logo SVG
function createLogoSVG(width = 200, height = 60) {
  return `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
    <!-- Background -->
    <rect width="${width}" height="${height}" rx="8" fill="#8B5CF6"/>
    
    <!-- Generator icon -->
    <g transform="translate(10, ${height/2 - 15}) scale(0.8)">
      <!-- Generator body -->
      <rect x="0" y="10" width="40" height="25" rx="3" fill="white" opacity="0.9"/>
      
      <!-- Control panel -->
      <rect x="3" y="13" width="12" height="8" rx="1" fill="#8B5CF6" opacity="0.8"/>
      
      <!-- Exhaust -->
      <rect x="35" y="8" width="4" height="10" rx="2" fill="white" opacity="0.7"/>
      
      <!-- Handle -->
      <rect x="38" y="22" width="5" height="2" rx="1" fill="white" opacity="0.8"/>
      
      <!-- Power symbol -->
      <circle cx="9" cy="17" r="1.5" fill="white"/>
      <path d="M9 16 L9 18 M7.5 17 L10.5 17" stroke="#8B5CF6" stroke-width="0.5" stroke-linecap="round"/>
    </g>
    
    <!-- Text -->
    <text x="60" y="${height/2 - 5}" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">RentalGenset</text>
    <text x="60" y="${height/2 + 10}" font-family="Arial, sans-serif" font-size="10" fill="white" opacity="0.8">Solusi Penyewaan Genset Terpercaya</text>
  </svg>`;
}

// Create simple logo for navbar
function createSimpleLogoSVG(width = 120, height = 40) {
  return `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
    <!-- Generator icon -->
    <g transform="translate(5, ${height/2 - 10}) scale(0.6)">
      <!-- Generator body -->
      <rect x="0" y="10" width="40" height="25" rx="3" fill="#8B5CF6"/>
      
      <!-- Control panel -->
      <rect x="3" y="13" width="12" height="8" rx="1" fill="white" opacity="0.9"/>
      
      <!-- Exhaust -->
      <rect x="35" y="8" width="4" height="10" rx="2" fill="#8B5CF6" opacity="0.7"/>
      
      <!-- Handle -->
      <rect x="38" y="22" width="5" height="2" rx="1" fill="#8B5CF6" opacity="0.8"/>
      
      <!-- Power symbol -->
      <circle cx="9" cy="17" r="1.5" fill="white"/>
    </g>
    
    <!-- Text -->
    <text x="35" y="${height/2 + 3}" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1F2937">RentalGenset</text>
  </svg>`;
}

async function generateLogos() {
  console.log('🎨 Generating logos...');
  
  // Create images directory if it doesn't exist
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  const logos = [
    { name: 'logo.png', svg: createLogoSVG(200, 60), description: 'Main logo' },
    { name: 'logo-simple.png', svg: createSimpleLogoSVG(120, 40), description: 'Simple navbar logo' },
    { name: 'logo-white.png', svg: createLogoSVG(200, 60).replace(/fill="#1F2937"/g, 'fill="white"'), description: 'White text logo' },
  ];

  let generated = 0;

  for (const logo of logos) {
    const outputPath = path.join(imagesDir, logo.name);
    
    if (fs.existsSync(outputPath)) {
      console.log(`⏭️  Skipping ${logo.name} (already exists)`);
      continue;
    }

    try {
      await sharp(Buffer.from(logo.svg))
        .png({ quality: 90 })
        .toFile(outputPath);
      
      console.log(`✅ Generated ${logo.name} - ${logo.description}`);
      generated++;
    } catch (error) {
      console.error(`❌ Error generating ${logo.name}:`, error.message);
    }
  }

  // Generate SVG versions too
  const svgLogos = [
    { name: 'logo.svg', svg: createLogoSVG(200, 60), description: 'Main logo SVG' },
    { name: 'logo-simple.svg', svg: createSimpleLogoSVG(120, 40), description: 'Simple navbar logo SVG' },
  ];

  for (const logo of svgLogos) {
    const outputPath = path.join(imagesDir, logo.name);
    
    if (fs.existsSync(outputPath)) {
      console.log(`⏭️  Skipping ${logo.name} (already exists)`);
      continue;
    }

    try {
      fs.writeFileSync(outputPath, logo.svg);
      console.log(`✅ Generated ${logo.name} - ${logo.description}`);
      generated++;
    } catch (error) {
      console.error(`❌ Error generating ${logo.name}:`, error.message);
    }
  }

  console.log(`\n🎉 Logo generation completed!`);
  console.log(`📊 Generated: ${generated} new logos`);
  console.log(`📁 Location: public/images/`);
  console.log(`\n💡 Available logos:`);
  console.log(`   - logo.png (200x60) - Main logo with background`);
  console.log(`   - logo-simple.png (120x40) - Navbar logo`);
  console.log(`   - logo-white.png (200x60) - White text version`);
  console.log(`   - logo.svg - Scalable main logo`);
  console.log(`   - logo-simple.svg - Scalable navbar logo`);
}

// Run the script
if (require.main === module) {
  generateLogos().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { generateLogos };
