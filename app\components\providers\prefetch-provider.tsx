'use client';

import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { apiCacheUtils, cacheKeys } from '@/lib/utils/performance-cache';

interface PrefetchProviderProps {
  children: React.ReactNode;
}

export function PrefetchProvider({ children }: PrefetchProviderProps) {
  const router = useRouter();

  // Prefetch critical API endpoints
  const prefetchCriticalData = useCallback(async () => {
    const criticalEndpoints = [
      '/api/products',
      '/api/user/profile',
    ];

    // Use requestIdleCallback for non-blocking prefetch
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        criticalEndpoints.forEach(async (endpoint) => {
          try {
            const response = await fetch(endpoint);
            if (response.ok) {
              const data = await response.json();
              // Cache the response
              apiCacheUtils.set(endpoint, data, 1000 * 60 * 5); // 5 minutes
            }
          } catch (error) {
            console.warn(`Failed to prefetch ${endpoint}:`, error);
          }
        });
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        criticalEndpoints.forEach(async (endpoint) => {
          try {
            const response = await fetch(endpoint);
            if (response.ok) {
              const data = await response.json();
              apiCacheUtils.set(endpoint, data, 1000 * 60 * 5);
            }
          } catch (error) {
            console.warn(`Failed to prefetch ${endpoint}:`, error);
          }
        });
      }, 1000);
    }
  }, []);

  // Prefetch pages based on user behavior
  const prefetchPages = useCallback(() => {
    const commonPages = [
      '/user/rentals',
      '/user/products',
      '/user/profile',
    ];

    commonPages.forEach((page) => {
      router.prefetch(page);
    });
  }, [router]);

  // Intelligent prefetching based on mouse hover
  const setupHoverPrefetch = useCallback(() => {
    // Skip if not in browser
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return () => { };
    }

    let hoverTimer: NodeJS.Timeout | null = null;

    // Simple and safe link detection
    const findLinkElement = (element: EventTarget | null): HTMLAnchorElement | null => {
      // Ensure we have a valid element
      if (!element || !(element instanceof HTMLElement)) {
        return null;
      }

      let current: HTMLElement | null = element;
      let depth = 0;
      const maxDepth = 5; // Prevent infinite loops

      while (current && depth < maxDepth) {
        // Check if current element is a link
        if (current.tagName === 'A' && current instanceof HTMLAnchorElement && current.href) {
          return current;
        }
        // Move to parent element
        current = current.parentElement;
        depth++;
      }
      return null;
    };

    const handleMouseOver = (event: MouseEvent) => {
      try {
        // Clear any existing timer
        if (hoverTimer) {
          clearTimeout(hoverTimer);
          hoverTimer = null;
        }

        // Find link element safely
        const link = findLinkElement(event.target);

        if (link && link.href) {
          try {
            const url = new URL(link.href);
            // Only prefetch internal links
            if (url.origin === window.location.origin && url.pathname !== window.location.pathname) {
              hoverTimer = setTimeout(() => {
                try {
                  router.prefetch(url.pathname);
                } catch (prefetchError) {
                  console.warn('Prefetch error:', url.pathname, prefetchError);
                }
                hoverTimer = null;
              }, 100); // Prefetch after 100ms hover
            }
          } catch (urlError) {
            // Invalid URL, skip prefetching
            console.warn('Invalid URL for prefetch:', link.href);
          }
        }
      } catch (error) {
        console.warn('Error in hover prefetch handler:', error);
      }
    };

    const handleMouseOut = () => {
      if (hoverTimer) {
        clearTimeout(hoverTimer);
        hoverTimer = null;
      }
    };

    try {
      // Add event listeners with passive option for better performance
      document.addEventListener('mouseover', handleMouseOver, { passive: true });
      document.addEventListener('mouseout', handleMouseOut, { passive: true });
    } catch (error) {
      console.warn('Error adding hover prefetch listeners:', error);
      return () => { };
    }

    return () => {
      try {
        document.removeEventListener('mouseover', handleMouseOver);
        document.removeEventListener('mouseout', handleMouseOut);
        if (hoverTimer) {
          clearTimeout(hoverTimer);
          hoverTimer = null;
        }
      } catch (error) {
        console.warn('Error removing hover prefetch listeners:', error);
      }
    };
  }, [router]);

  // Prefetch based on viewport intersection
  const setupViewportPrefetch = useCallback(() => {
    // Check for IntersectionObserver support
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return () => { }; // Return empty cleanup function
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target;

            // Ensure element is HTMLElement with dataset
            if (!(element instanceof HTMLElement) || !element.dataset) {
              return;
            }

            const prefetchUrl = element.dataset.prefetch;

            if (prefetchUrl && typeof prefetchUrl === 'string') {
              try {
                // Validate URL before prefetching
                const url = new URL(prefetchUrl, window.location.origin);
                if (url.origin === window.location.origin) {
                  router.prefetch(url.pathname);
                  observer.unobserve(element);
                }
              } catch (error) {
                console.warn('Invalid prefetch URL:', prefetchUrl, error);
              }
            }
          }
        });
      },
      {
        rootMargin: '200px 0px', // Start prefetching 200px before element is visible
        threshold: 0.01,
      }
    );

    // Find and observe elements with data-prefetch attribute
    try {
      const prefetchElements = document.querySelectorAll('[data-prefetch]');
      prefetchElements.forEach((element) => {
        if (element instanceof HTMLElement) {
          observer.observe(element);
        }
      });
    } catch (error) {
      console.warn('Error setting up viewport prefetch:', error);
    }

    return () => {
      try {
        observer.disconnect();
      } catch (error) {
        console.warn('Error disconnecting viewport observer:', error);
      }
    };
  }, [router]);

  // Preload critical resources
  const preloadCriticalResources = useCallback(() => {
    const criticalResources = [
      { href: '/images/logo.png', as: 'image' },
      { href: '/sound/notification.mp3', as: 'audio' },
      { href: '/_next/static/css/app.css', as: 'style' },
    ];

    criticalResources.forEach(({ href, as }) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = as;

      // Add to head if not already present
      if (!document.querySelector(`link[href="${href}"]`)) {
        document.head.appendChild(link);
      }
    });
  }, []);

  // Setup connection warming
  const setupConnectionWarming = useCallback(() => {
    const criticalOrigins = [
      'https://api.sandbox.midtrans.com',
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
    ];

    criticalOrigins.forEach((origin) => {
      // DNS prefetch
      const dnsLink = document.createElement('link');
      dnsLink.rel = 'dns-prefetch';
      dnsLink.href = origin;

      // Preconnect
      const preconnectLink = document.createElement('link');
      preconnectLink.rel = 'preconnect';
      preconnectLink.href = origin;
      preconnectLink.crossOrigin = 'anonymous';

      if (!document.querySelector(`link[href="${origin}"]`)) {
        document.head.appendChild(dnsLink);
        document.head.appendChild(preconnectLink);
      }
    });
  }, []);

  // Adaptive prefetching based on connection speed
  const setupAdaptivePrefetch = useCallback(() => {
    if ('connection' in navigator) {
      // @ts-ignore
      const connection = navigator.connection;

      // Only prefetch on fast connections
      if (connection.effectiveType === '4g' || connection.effectiveType === '3g') {
        prefetchCriticalData();
        prefetchPages();
      }

      // Listen for connection changes
      connection.addEventListener('change', () => {
        if (connection.effectiveType === '4g') {
          prefetchCriticalData();
        }
      });
    } else {
      // Fallback: assume good connection
      prefetchCriticalData();
      prefetchPages();
    }
  }, [prefetchCriticalData, prefetchPages]);

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    // Initialize all prefetching strategies
    const cleanupFunctions: (() => void)[] = [];

    // Setup with comprehensive error handling
    const initializePrefetching = async () => {
      try {
        // Setup hover prefetching
        const hoverCleanup = setupHoverPrefetch();
        if (typeof hoverCleanup === 'function') {
          cleanupFunctions.push(hoverCleanup);
        }

        // Setup viewport prefetching
        const viewportCleanup = setupViewportPrefetch();
        if (typeof viewportCleanup === 'function') {
          cleanupFunctions.push(viewportCleanup);
        }

        // Preload critical resources immediately
        try {
          preloadCriticalResources();
        } catch (error) {
          console.warn('Error preloading critical resources:', error);
        }

        try {
          setupConnectionWarming();
        } catch (error) {
          console.warn('Error setting up connection warming:', error);
        }

        // Setup adaptive prefetching
        try {
          setupAdaptivePrefetch();
        } catch (error) {
          console.warn('Error setting up adaptive prefetch:', error);
        }
      } catch (error) {
        console.warn('Error initializing prefetch strategies:', error);
      }
    };

    // Initialize prefetching
    initializePrefetching();

    // Cleanup function
    return () => {
      cleanupFunctions.forEach((cleanup, index) => {
        try {
          cleanup();
        } catch (error) {
          console.warn(`Error during prefetch cleanup ${index}:`, error);
        }
      });
    };
  }, [
    setupHoverPrefetch,
    setupViewportPrefetch,
    preloadCriticalResources,
    setupConnectionWarming,
    setupAdaptivePrefetch,
  ]);

  return <>{children}</>;
}

// Hook for manual prefetching
export function usePrefetch() {
  const router = useRouter();

  const prefetchPage = useCallback((href: string) => {
    router.prefetch(href);
  }, [router]);

  const prefetchData = useCallback(async (endpoint: string, cacheKey?: string) => {
    try {
      const response = await fetch(endpoint);
      if (response.ok) {
        const data = await response.json();
        apiCacheUtils.set(cacheKey || endpoint, data, 1000 * 60 * 5);
        return data;
      }
    } catch (error) {
      console.warn(`Failed to prefetch data from ${endpoint}:`, error);
    }
  }, []);

  const prefetchResource = useCallback((href: string, as: string) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    link.as = as;
    document.head.appendChild(link);
  }, []);

  return {
    prefetchPage,
    prefetchData,
    prefetchResource,
  };
}

// Component for adding prefetch hints to links
export function PrefetchLink({
  href,
  children,
  className,
  prefetchOnHover = true,
  ...props
}: {
  href: string;
  children: React.ReactNode;
  className?: string;
  prefetchOnHover?: boolean;
  [key: string]: any;
}) {
  const router = useRouter();

  const handleMouseEnter = useCallback(() => {
    if (prefetchOnHover && href) {
      try {
        router.prefetch(href);
      } catch (error) {
        console.warn('Error prefetching link:', href, error);
      }
    }
  }, [href, prefetchOnHover, router]);

  const handleClick = useCallback((event: React.MouseEvent<HTMLAnchorElement>) => {
    // Let Next.js handle the navigation
    if (props.onClick) {
      props.onClick(event);
    }
  }, [props]);

  return (
    <a
      href={href}
      className={className}
      onMouseEnter={handleMouseEnter}
      onClick={handleClick}
      {...props}
    >
      {children}
    </a>
  );
}
