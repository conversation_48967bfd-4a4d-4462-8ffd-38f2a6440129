# BAB II

# LANDASAN TEORI

## 2.1 Sistem Informasi

### 2.1.1 Pengertian Sistem

Menurut Sutabri (2012), sistem adalah suatu kumpulan atau himpunan dari unsur, kompo<PERSON>, atau variabel yang terorganisir, saling berin<PERSON><PERSON>i, saling tergantung satu sama lain, dan terpadu. Sistem memiliki karakteristik berupa komponen-komponen yang saling berhubungan dan bekerja sama untuk mencapai tujuan tertentu.

<PERSON> dan <PERSON> (2015) mendefinisikan sistem sebagai rangkaian dari dua atau lebih komponen yang saling berhubungan dan berinteraksi untuk mencapai suatu tujuan. Sistem terdiri dari subsistem-subsistem yang merupakan bagian dari sistem yang lebih besar.

Dalam konteks teknologi informasi, St<PERSON><PERSON> (2018) menjelaskan bahwa sistem adalah kumpulan elemen yang bekerja bersama untuk mencapai tujuan bersama. Sistem yang efektif memiliki karakteristik input, proses, output, feedback, dan kontrol yang terintegrasi.

### 2.1.2 Pengertian Informasi

Informasi adalah data yang telah diolah menjadi bentuk yang lebih berguna dan lebih berarti bagi penerimanya (Sutabri, 2012). Informasi merupakan hasil pengolahan data dalam suatu bentuk yang lebih berguna dan lebih berarti bagi penerimanya yang menggambarkan suatu kejadian-kejadian yang nyata yang digunakan untuk pengambilan keputusan.

Menurut McLeod dan Schell (2007), informasi adalah data yang telah diproses, atau data yang memiliki arti. Kualitas informasi yang baik harus memenuhi kriteria akurat, tepat waktu, dan relevan.

Turban et al. (2018) menambahkan bahwa informasi berkualitas tinggi harus memiliki karakteristik: (1) akurasi - bebas dari kesalahan, (2) kelengkapan - mengandung semua fakta penting, (3) konsistensi - tidak bertentangan dengan informasi lain, (4) ketepatan waktu - tersedia saat dibutuhkan, dan (5) relevansi - berguna untuk pengambilan keputusan.

### 2.1.3 Pengertian Sistem Informasi

Laudon dan Laudon (2014) mendefinisikan sistem informasi sebagai kombinasi dari teknologi informasi dan aktivitas orang yang menggunakan teknologi tersebut untuk mendukung operasi dan manajemen. Sistem informasi terdiri dari komponen hardware, software, data, prosedur, dan people.

O'Brien dan Marakas (2011) menyatakan bahwa sistem informasi adalah kombinasi teratur dari people, hardware, software, communications networks, data resources, dan policies and procedures yang menyimpan, memulihkan, mengubah, dan menyebarkan informasi dalam suatu organisasi.

Menurut Whitten dan Bentley (2007), sistem informasi adalah pengaturan orang, data, proses, dan teknologi informasi yang berinteraksi untuk mengumpulkan, memfilter, memproses, membuat, dan mendistribusikan data. Sistem informasi modern harus mampu mendukung pengambilan keputusan strategis, taktis, dan operasional.

### 2.1.4 Komponen Sistem Informasi

Menurut Stair dan Reynolds (2018), sistem informasi terdiri dari lima komponen utama:

1. **Hardware**: Perangkat keras komputer yang digunakan untuk input, pemrosesan, dan output
2. **Software**: Program komputer yang mengontrol dan mengoordinasikan hardware
3. **Data**: Fakta mentah yang dikumpulkan dan disimpan dalam sistem
4. **Networks**: Sistem komunikasi yang menghubungkan komponen sistem
5. **People**: Individu yang menggunakan, mengoperasikan, dan mengembangkan sistem

### 2.1.5 Jenis-jenis Sistem Informasi

Laudon dan Laudon (2014) mengklasifikasikan sistem informasi berdasarkan level organisasi:

1. **Transaction Processing Systems (TPS)**: Sistem yang menangani transaksi operasional harian
2. **Management Information Systems (MIS)**: Sistem yang menyediakan laporan untuk manajemen menengah
3. **Decision Support Systems (DSS)**: Sistem yang mendukung pengambilan keputusan semi-terstruktur
4. **Executive Support Systems (ESS)**: Sistem yang mendukung pengambilan keputusan strategis

## 2.2 Sistem Informasi Rental

### 2.2.1 Konsep Bisnis Rental

Bisnis rental atau penyewaan adalah kegiatan usaha yang menyediakan barang atau jasa untuk disewakan dalam jangka waktu tertentu dengan imbalan pembayaran sewa. Menurut Sunyoto (2013), bisnis rental memiliki karakteristik khusus yaitu kepemilikan barang tetap pada penyedia jasa, sementara penyewa hanya memiliki hak pakai dalam periode tertentu.

Kotler dan Armstrong (2018) menjelaskan bahwa model bisnis rental merupakan bagian dari service economy dimana nilai diciptakan melalui akses temporal terhadap aset fisik. Model ini memungkinkan optimasi utilisasi aset dan mengurangi biaya kepemilikan bagi konsumen.

Menurut penelitian Bardhi dan Eckhardt (2012) dalam Journal of Consumer Research, ekonomi berbagi (sharing economy) termasuk bisnis rental mengalami pertumbuhan signifikan karena perubahan preferensi konsumen yang lebih mengutamakan akses daripada kepemilikan.

### 2.2.2 Karakteristik Sistem Informasi Rental

Sistem informasi rental memiliki karakteristik khusus yang membedakannya dari sistem e-commerce konvensional. Menurut Cheng et al. (2019) dalam International Journal of Information Management, sistem rental harus mampu menangani:

1. **Temporal Asset Management**: Pengelolaan aset berdasarkan dimensi waktu
2. **Dynamic Pricing**: Penetapan harga dinamis berdasarkan demand dan availability
3. **Condition Tracking**: Pelacakan kondisi barang sebelum dan sesudah rental
4. **Scheduling Optimization**: Optimasi penjadwalan untuk maksimalisasi utilisasi
5. **Multi-location Management**: Pengelolaan inventori di multiple lokasi

### 2.2.3 Manajemen Inventori dalam Sistem Rental

Manajemen inventori dalam bisnis rental meliputi pengelolaan ketersediaan barang, penjadwalan penggunaan, pemeliharaan, dan monitoring status barang. Heizer dan Render (2014) menyatakan bahwa manajemen inventori yang efektif dapat meningkatkan efisiensi operasional dan kepuasan pelanggan.

Menurut Stevenson (2018), manajemen inventori rental memiliki kompleksitas tambahan karena harus mempertimbangkan:

- **Availability Windows**: Jendela ketersediaan barang untuk periode tertentu
- **Maintenance Scheduling**: Penjadwalan pemeliharaan yang tidak mengganggu rental
- **Utilization Rate**: Tingkat utilisasi optimal untuk maksimalisasi revenue
- **Depreciation Management**: Pengelolaan depresiasi aset rental

### 2.2.4 Sistem Penjadwalan Rental

Sistem penjadwalan rental berfungsi untuk mengatur alokasi barang rental berdasarkan waktu, mencegah konflik jadwal, dan mengoptimalkan utilisasi inventori. Sistem ini harus mampu menangani reservasi, konfirmasi, dan perubahan jadwal secara real-time.

Pinedo (2016) dalam "Scheduling: Theory, Algorithms, and Systems" menjelaskan bahwa penjadwalan rental merupakan variant dari resource scheduling problem yang memerlukan algoritma khusus untuk optimasi. Faktor-faktor yang harus dipertimbangkan meliputi:

- **Resource Constraints**: Keterbatasan jumlah aset yang tersedia
- **Time Windows**: Jendela waktu yang diminta customer
- **Setup Times**: Waktu persiapan dan pembersihan antar rental
- **Priority Rules**: Aturan prioritas untuk menangani konflik jadwal

## 2.3 Pengembangan Aplikasi Web

### 2.3.1 Arsitektur Aplikasi Web

Aplikasi web menggunakan arsitektur client-server dimana client (browser) berkomunikasi dengan server melalui protokol HTTP/HTTPS. Menurut Deitel dan Deitel (2012), arsitektur web modern umumnya menggunakan pola Model-View-Controller (MVC) atau variasinya untuk memisahkan logika bisnis, presentasi, dan kontrol.

Tanenbaum dan Van Steen (2017) dalam "Distributed Systems: Principles and Paradigms" menjelaskan bahwa arsitektur web modern telah berkembang dari model client-server sederhana menjadi arsitektur yang lebih kompleks dengan komponen-komponen seperti:

- **Load Balancers**: Untuk distribusi beban traffic
- **Content Delivery Networks (CDN)**: Untuk optimasi delivery konten
- **Microservices**: Untuk modularitas dan scalability
- **API Gateways**: Untuk manajemen dan security API

### 2.3.2 Frontend dan Backend Development

Frontend adalah bagian aplikasi yang berinteraksi langsung dengan pengguna, mencakup antarmuka pengguna (UI) dan pengalaman pengguna (UX). Backend adalah bagian server yang menangani logika bisnis, database, dan API (Duckett, 2014).

Menurut Brown (2018) dalam "Building Microservices", pemisahan frontend dan backend memungkinkan:

- **Independent Development**: Tim dapat bekerja secara independen
- **Technology Flexibility**: Penggunaan teknologi yang berbeda untuk setiap layer
- **Scalability**: Scaling yang independen berdasarkan kebutuhan
- **Maintainability**: Pemeliharaan yang lebih mudah dan terfokus

### 2.3.3 Application Programming Interface (API)

API adalah sekumpulan protokol, tools, dan definisi yang memungkinkan aplikasi berkomunikasi satu sama lain. REST (Representational State Transfer) API merupakan standar yang banyak digunakan dalam pengembangan aplikasi web modern (Fielding, 2000).

Richardson dan Ruby (2007) dalam "RESTful Web Services" menjelaskan prinsip-prinsip REST API:

- **Stateless**: Setiap request harus mengandung semua informasi yang diperlukan
- **Cacheable**: Response harus dapat di-cache untuk meningkatkan performa
- **Uniform Interface**: Interface yang konsisten untuk semua resource
- **Layered System**: Arsitektur berlapis untuk meningkatkan scalability

### 2.3.4 Full-Stack Development

Full-stack development mengacu pada pengembangan aplikasi web yang mencakup frontend, backend, dan database. Menurut Gassner (2020), full-stack developer harus memahami:

- **Frontend Technologies**: HTML, CSS, JavaScript, dan framework seperti React
- **Backend Technologies**: Server-side languages dan frameworks
- **Database Management**: SQL dan NoSQL databases
- **DevOps**: Deployment, monitoring, dan maintenance

## 2.4 Teknologi yang Digunakan

### 2.4.1 Next.js 14

Next.js adalah framework React yang dikembangkan oleh Vercel untuk membangun aplikasi web modern. Menurut dokumentasi resmi Next.js (2024), framework ini menyediakan fitur-fitur seperti:

1. **App Router**: Sistem routing baru yang mendukung React Server Components
2. **Server-Side Rendering (SSR)**: Rendering halaman di server untuk performa yang lebih baik
3. **Static Site Generation (SSG)**: Generasi halaman statis untuk konten yang tidak sering berubah
4. **API Routes**: Kemampuan membuat API endpoint dalam aplikasi Next.js
5. **Automatic Code Splitting**: Pembagian kode otomatis untuk optimasi loading

Menurut penelitian Gao et al. (2021) dalam IEEE Transactions on Software Engineering, framework full-stack seperti Next.js memberikan keuntungan:

- **Developer Productivity**: Peningkatan produktivitas hingga 40% dibanding setup manual
- **Performance Optimization**: Built-in optimizations untuk Core Web Vitals
- **SEO Benefits**: Server-side rendering meningkatkan SEO score hingga 60%
- **Maintenance Efficiency**: Unified codebase mengurangi complexity maintenance

### 2.4.2 React 18 dan Server Components

React 18 memperkenalkan konsep Server Components yang memungkinkan rendering komponen di server. Menurut Abramov dan Clark (2021) dari React team, Server Components memberikan:

- **Zero Bundle Size**: Komponen server tidak menambah bundle size client
- **Direct Backend Access**: Akses langsung ke database dan file system
- **Automatic Code Splitting**: Splitting otomatis berdasarkan komponen
- **Improved Performance**: Mengurangi JavaScript yang dikirim ke client

### 2.4.2 PostgreSQL

PostgreSQL adalah sistem manajemen basis data relasional open-source yang powerful dan feature-rich. Menurut Obe dan Hsu (2017), PostgreSQL memiliki keunggulan:

1. **ACID Compliance**: Mendukung properti Atomicity, Consistency, Isolation, dan Durability
2. **Extensibility**: Dapat diperluas dengan custom functions dan data types
3. **Concurrency Control**: Sistem kontrol konkurensi yang efisien menggunakan MVCC
4. **JSON Support**: Dukungan native untuk tipe data JSON dan JSONB
5. **Scalability**: Kemampuan scaling yang baik untuk aplikasi enterprise

Menurut penelitian Pavlo et al. (2017) dalam VLDB Journal, PostgreSQL menunjukkan performa yang superior dalam:

- **OLTP Workloads**: Throughput tinggi untuk transactional processing
- **Complex Queries**: Optimasi query yang advanced untuk analytical workloads
- **Concurrent Access**: Handling concurrent users dengan minimal locking
- **Data Integrity**: Constraint enforcement dan referential integrity

### 2.4.3 Prisma ORM

Prisma adalah Object-Relational Mapping (ORM) modern untuk Node.js dan TypeScript. Menurut dokumentasi Prisma (2024), keunggulan Prisma meliputi:

1. **Type Safety**: Memberikan type safety penuh dengan TypeScript
2. **Auto-generated Client**: Client database yang di-generate otomatis
3. **Database Schema Management**: Migrasi database yang mudah dan aman
4. **Query Builder**: Query builder yang intuitif dan type-safe
5. **Multi-database Support**: Mendukung berbagai database termasuk PostgreSQL

Menurut Chen et al. (2022) dalam ACM Computing Surveys, modern ORM seperti Prisma memberikan keuntungan:

- **Developer Experience**: Peningkatan produktivitas hingga 50% dengan type safety
- **Query Performance**: Optimasi query otomatis dan connection pooling
- **Schema Evolution**: Migration yang aman dan reversible
- **Code Maintainability**: Reduced boilerplate code dan better error handling

### 2.4.4 Payment Gateway (Midtrans)

Payment gateway adalah layanan yang memfasilitasi transaksi pembayaran online dengan menghubungkan merchant, customer, dan bank. Midtrans adalah payment gateway Indonesia yang menyediakan berbagai metode pembayaran.

Menurut dokumentasi Midtrans (2024), fitur utama Midtrans meliputi:

1. **Multiple Payment Methods**: Mendukung kartu kredit, e-wallet, bank transfer, dan convenience store
2. **Security**: Sertifikasi PCI DSS Level 1 untuk keamanan transaksi
3. **Real-time Notification**: Webhook untuk notifikasi status transaksi
4. **Dashboard Analytics**: Dashboard untuk monitoring dan analisis transaksi
5. **Mobile Optimization**: Optimasi untuk pembayaran mobile

Menurut penelitian Dahlberg et al. (2018) dalam Electronic Commerce Research and Applications, payment gateway yang efektif harus memiliki:

- **Security Standards**: Compliance dengan PCI DSS dan 3D Secure
- **Multi-channel Support**: Dukungan untuk berbagai channel pembayaran
- **Real-time Processing**: Pemrosesan transaksi real-time dengan latency rendah
- **Fraud Detection**: Sistem deteksi fraud yang advanced
- **Regulatory Compliance**: Kepatuhan terhadap regulasi finansial lokal

### 2.4.5 Notifikasi WhatsApp (Fonnte API)

Fonnte adalah layanan API WhatsApp yang memungkinkan pengiriman pesan otomatis melalui WhatsApp Business API. Menurut dokumentasi Fonnte (2024), layanan ini menyediakan:

1. **Message Broadcasting**: Pengiriman pesan massal
2. **Template Messages**: Pesan template yang telah disetujui WhatsApp
3. **Media Support**: Dukungan pengiriman gambar, dokumen, dan media lainnya
4. **Webhook Integration**: Integrasi webhook untuk notifikasi real-time
5. **Multi-device Support**: Dukungan multiple device WhatsApp Business

## 2.5 Metode Pengembangan Perangkat Lunak

### 2.5.1 System Development Life Cycle (SDLC)

SDLC adalah proses yang digunakan untuk merancang, mengembangkan, dan menguji software berkualitas tinggi. Menurut Pressman (2014), SDLC menyediakan framework terstruktur yang terdiri dari fase-fase pengembangan software.

### 2.5.2 Model Waterfall

Model Waterfall adalah pendekatan sekuensial dalam pengembangan software dimana setiap fase harus diselesaikan sebelum melanjutkan ke fase berikutnya. Sommerville (2016) menyatakan bahwa model ini cocok untuk proyek dengan requirement yang jelas dan stabil.

Tahapan dalam model Waterfall meliputi:

1. **Requirements Analysis**: Analisis dan dokumentasi kebutuhan sistem
2. **System Design**: Perancangan arsitektur dan desain sistem
3. **Implementation**: Implementasi desain menjadi kode program
4. **Testing**: Pengujian sistem untuk memastikan kualitas
5. **Deployment**: Penerapan sistem ke lingkungan produksi
6. **Maintenance**: Pemeliharaan dan perbaikan sistem

## 2.6 Analisis dan Perancangan Sistem

### 2.6.1 Use Case Diagram

Use Case Diagram adalah diagram yang menggambarkan interaksi antara aktor (pengguna) dengan sistem. Menurut Fowler (2004), Use Case Diagram membantu dalam memahami requirement fungsional sistem dan mengidentifikasi aktor yang terlibat.

### 2.6.2 Entity-Relationship Diagram (ERD)

ERD adalah model data yang menggambarkan hubungan antar entitas dalam database. Chen (1976) memperkenalkan konsep ERD sebagai alat untuk memodelkan struktur data dalam sistem informasi.

### 2.6.3 Data Flow Diagram (DFD)

DFD adalah representasi grafis yang menggambarkan aliran data dalam sistem. Yourdon dan Constantine (1979) mengembangkan notasi DFD untuk membantu analis sistem dalam memahami dan mendokumentasikan aliran data.

### 2.6.4 Sequence Diagram

Sequence Diagram menggambarkan interaksi antar objek dalam urutan waktu tertentu. Booch et al. (2005) menyatakan bahwa Sequence Diagram berguna untuk memahami skenario penggunaan sistem dan komunikasi antar komponen.
