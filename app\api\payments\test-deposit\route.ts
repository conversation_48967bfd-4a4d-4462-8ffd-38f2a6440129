import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";

export async function POST(request: Request) {
  try {
    const session = await getSession();
    
    // Only allow admin users to access this test endpoint
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { rentalId } = await request.json();

    if (!rentalId) {
      return NextResponse.json({ error: "rentalId is required" }, { status: 400 });
    }

    // Find the payment for this rental
    const payment = await prisma.payment.findUnique({
      where: { rentalId },
      include: {
        rental: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
                phone: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Simulate successful deposit payment
    const updatedPayment = await prisma.$transaction(async (tx) => {
      // Update payment status to DEPOSIT_PAID
      const updated = await tx.payment.update({
        where: { id: payment.id },
        data: {
          status: "DEPOSIT_PAID",
          transactionId: `TEST_DEPOSIT_${Date.now()}`,
          updatedAt: new Date()
        }
      });

      // Update rental status to ACTIVE
      await tx.rental.update({
        where: { id: rentalId },
        data: {
          status: "ACTIVE"
        }
      });

      return updated;
    });

    return NextResponse.json({
      success: true,
      message: "Deposit payment simulated successfully",
      payment: {
        id: updatedPayment.id,
        status: updatedPayment.status,
        transactionId: updatedPayment.transactionId,
        rental: {
          id: payment.rental.id,
          user: payment.rental.user,
          product: payment.rental.product
        }
      }
    });
  } catch (error) {
    console.error("[TEST_DEPOSIT_ERROR]", error);
    return NextResponse.json(
      { error: "Failed to simulate deposit payment" },
      { status: 500 }
    );
  }
}
