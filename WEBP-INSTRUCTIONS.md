
# WebP Conversion Instructions

## For existing images:
1. Install sharp: npm install sharp
2. Run conversion script: node scripts/convert-to-webp.js

## For new images:
- Always upload in WebP format when possible
- Use online converters like squoosh.app for manual conversion
- Set quality to 85-90 for best balance of size/quality

## Generated files:
- testimonial-1.svg (<PERSON>)
- testimonial-2.svg (<PERSON><PERSON>)  
- testimonial-3.svg (Budi <PERSON> - <PERSON>)
- product-placeholder.svg (Generic genset placeholder)

These SVG files are lightweight and will work as fallbacks.
