import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import MidtransService from "@/lib/services/midtrans";
import { calculateOvertimeCost } from "@/lib/utils/calculate";

export const runtime = 'nodejs';

// POST /api/payments/full/[rentalId] - Create full payment
export async function POST(
  request: Request,
  { params }: { params: Promise<{ rentalId: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { rentalId } = await params;
    
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: { 
        payment: true,
        product: true,
        user: true
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Rental tidak ditemukan" }, { status: 404 });
    }

    // Pastikan user yang login adalah pemilik rental
    if (rental.userId !== session.user.id) {
      return NextResponse.json({ error: "Tidak memiliki akses ke rental ini" }, { status: 403 });
    }

    // Cek apakah sudah ada pembayaran yang lunas
    if (rental.payment && rental.payment.status === 'FULLY_PAID') {
      return NextResponse.json({ error: "Pembayaran sudah lunas" }, { status: 400 });
    }

    // Hitung total pembayaran + overtime
    const overtimeHours = rental.overtimeHours || 0;
    let overtimeCost = 0;

    if (overtimeHours > 0) {
      // Jika ada payment.overtimeCost yang sudah dihitung sebelumnya, gunakan itu
      if (rental.payment?.overtimeCost) {
        overtimeCost = rental.payment.overtimeCost;
      } else {
        // Jika tidak, hitung ulang berdasarkan overtime dan tarif produk
        overtimeCost = calculateOvertimeCost(overtimeHours, rental.product.price);
      }
    }
    
    const fullAmount = rental.amount + overtimeCost;

    // Buat token pembayaran Midtrans
    const payment = await MidtransService.createPayment({
      orderId: `${rental.id}_full_${Date.now()}`,
      amount: fullAmount,
      email: session.user.email || "",
      name: session.user.name || "Customer",
      productName: `Pembayaran Penuh Rental Genset - ${rental.product.name}`
    });

    // Update atau buat record payment
    await prisma.payment.upsert({
      where: { rentalId: rental.id },
      update: {
        amount: fullAmount,
        deposit: fullAmount,
        remaining: 0,
        overtimeCost: overtimeCost,
        transactionId: payment.token,
        status: 'DEPOSIT_PENDING',
        updatedAt: new Date()
      },
      create: {
        rentalId: rental.id,
        amount: fullAmount,
        deposit: fullAmount,
        remaining: 0,
        overtimeCost: overtimeCost,
        transactionId: payment.token,
        status: 'DEPOSIT_PENDING',
        userId: session.user.id
      }
    });

    return NextResponse.json({ 
      success: true,
      token: payment.token,
      message: "Token pembayaran berhasil dibuat"
    });

  } catch (error) {
    console.error("[FULL_PAYMENT_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal memproses pembayaran penuh" },
      { status: 500 }
    );
  }
}
