'use client';

import { cn } from '@/lib/utils';

interface TestimonialAvatarProps {
  name: string;
  className?: string;
}

/**
 * Simple testimonial avatar component that prevents hydration mismatch
 * Uses initials instead of dynamic images for consistency
 */
export function TestimonialAvatar({ name, className }: TestimonialAvatarProps) {
  // Get initials from name
  const getInitials = (fullName: string): string => {
    return fullName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate consistent color based on name
  const getAvatarColor = (fullName: string): string => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-orange-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500',
      'bg-red-500',
    ];

    // Simple hash function for consistent color
    let hash = 0;
    for (let i = 0; i < fullName.length; i++) {
      hash = fullName.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  const initials = getInitials(name);
  const colorClass = getAvatarColor(name);

  return (
    <div
      className={cn(
        'absolute inset-0 rounded-full flex items-center justify-center text-white font-bold text-xl',
        colorClass,
        className
      )}
      suppressHydrationWarning
    >
      {initials}
    </div>
  );
}

/**
 * Testimonial card component with consistent avatar
 */
interface TestimonialCardProps {
  name: string;
  role: string;
  content: string;
  className?: string;
}

export function TestimonialCard({ name, role, content, className }: TestimonialCardProps) {
  return (
    <div className={cn('flex flex-col md:flex-row items-center', className)} suppressHydrationWarning>
      <div className="md:w-1/3 p-6">
        <div className="relative h-24 w-24 rounded-full overflow-hidden mx-auto mb-4">
          <TestimonialAvatar name={name} />
        </div>
        <h3 className="text-xl font-bold text-center text-gray-900 dark:text-gray-100">
          {name}
        </h3>
        <p className="text-blue-600 dark:text-blue-400 text-center">
          {role}
        </p>
      </div>
      <div className="md:w-2/3 p-6">
        <p className="text-xl italic text-gray-700 dark:text-gray-300">
          &ldquo;{content}&rdquo;
        </p>
      </div>
    </div>
  );
}
