'use client';

import { lazy, Suspense } from 'react';
import { LoadingSpinner } from '@/app/components/ui/loading-spinner';

// Lazy load heavy components
export const Lazy<PERSON>hart = lazy(() => 
  import('react-chartjs-2').then(module => ({ default: module.Chart }))
);

export const LazyMap = lazy(() => 
  import('react-map-gl').then(module => ({ default: module.Map }))
);

export const LazyPDFViewer = lazy(() => 
  import('html2pdf.js').then(module => ({ default: module.default }))
);

export const LazyGSAP = lazy(() => 
  import('gsap').then(module => ({ default: module.gsap }))
);

// Wrapper components with loading states
export function ChartWrapper({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <LazyChart {...props}>
        {children}
      </LazyChart>
    </Suspense>
  );
}

export function MapWrapper({ children, ...props }: any) {
  return (
    <Suspense fallback={<MapSkeleton />}>
      <LazyMap {...props}>
        {children}
      </LazyMap>
    </Suspense>
  );
}

export function PDFWrapper({ children, ...props }: any) {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <LazyPDFViewer {...props}>
        {children}
      </LazyPDFViewer>
    </Suspense>
  );
}

// Skeleton components
function ChartSkeleton() {
  return (
    <div className="w-full h-64 bg-gray-200 dark:bg-gray-800 animate-pulse rounded-lg flex items-center justify-center">
      <div className="text-gray-500 dark:text-gray-400">Loading chart...</div>
    </div>
  );
}

function MapSkeleton() {
  return (
    <div className="w-full h-96 bg-gray-200 dark:bg-gray-800 animate-pulse rounded-lg flex items-center justify-center">
      <div className="text-gray-500 dark:text-gray-400">Loading map...</div>
    </div>
  );
}

// Dynamic import utilities
export const dynamicImport = {
  // Chart libraries
  chartjs: () => import('chart.js'),
  recharts: () => import('recharts'),
  
  // Map libraries
  maplibre: () => import('maplibre-gl'),
  leaflet: () => import('leaflet'),
  
  // Animation libraries
  gsap: () => import('gsap'),
  framerMotion: () => import('framer-motion'),
  
  // PDF libraries
  html2pdf: () => import('html2pdf.js'),
  jspdf: () => import('jspdf'),
  
  // Form libraries
  reactHookForm: () => import('react-hook-form'),
  
  // Date libraries
  dateFns: () => import('date-fns'),
  
  // Utility libraries
  lodash: () => import('lodash'),
};

// Preload critical modules
export function preloadCriticalModules() {
  if (typeof window !== 'undefined') {
    // Preload modules that are likely to be needed soon
    const criticalModules = [
      'react-hook-form',
      'date-fns',
      'framer-motion',
    ];

    criticalModules.forEach(moduleName => {
      if (moduleName in dynamicImport) {
        // @ts-ignore
        dynamicImport[moduleName]().catch(() => {
          // Silently fail if module can't be preloaded
        });
      }
    });
  }
}

// Bundle analyzer helper
export function analyzeBundleSize() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Bundle analysis available in production build');
    return;
  }

  // This would be used with webpack-bundle-analyzer
  console.log('Bundle size analysis would be available here');
}

// Tree shaking helper - mark unused exports
export const UNUSED_EXPORTS = {
  // Mark exports that should be tree-shaken
  unusedChartTypes: ['PolarArea', 'Radar'],
  unusedMapFeatures: ['3D', 'Terrain'],
  unusedAnimations: ['Bounce', 'Elastic'],
};

// Code splitting by route
export const routeBasedSplitting = {
  admin: () => import('@/app/(dashboard)/admin/layout'),
  user: () => import('@/app/(dashboard)/user/layout'),
  auth: () => import('@/app/(auth)/layout'),
  marketing: () => import('@/app/(marketing)/layout'),
};

// Feature-based splitting
export const featureBasedSplitting = {
  payments: () => import('@/app/components/payment'),
  rentals: () => import('@/app/components/rental'),
  products: () => import('@/app/components/product'),
  maps: () => import('@/app/components/map'),
  charts: () => import('@/app/components/ui/rental-trend-chart'),
};

// Vendor splitting configuration
export const vendorSplitting = {
  react: ['react', 'react-dom'],
  ui: ['@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
  charts: ['chart.js', 'react-chartjs-2', 'recharts'],
  maps: ['maplibre-gl', 'react-map-gl', 'leaflet'],
  animations: ['gsap', 'framer-motion'],
  forms: ['react-hook-form', '@hookform/resolvers'],
  utils: ['date-fns', 'lodash', 'clsx'],
};

// Performance monitoring for code splitting
export function monitorCodeSplitting() {
  if ('performance' in window) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('chunk') || entry.name.includes('lazy')) {
          console.log(`Lazy chunk loaded: ${entry.name} in ${entry.duration}ms`);
        }
      });
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }
}

// Initialize bundle optimization
export function initializeBundleOptimization() {
  if (typeof window !== 'undefined') {
    // Preload critical modules
    preloadCriticalModules();
    
    // Monitor code splitting performance
    monitorCodeSplitting();
    
    // Set up intersection observer for lazy loading
    const lazyElements = document.querySelectorAll('[data-lazy]');
    const lazyObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;
          const moduleName = element.dataset.lazy;
          
          if (moduleName && moduleName in featureBasedSplitting) {
            // @ts-ignore
            featureBasedSplitting[moduleName]().then(() => {
              element.removeAttribute('data-lazy');
              lazyObserver.unobserve(element);
            });
          }
        }
      });
    }, {
      rootMargin: '100px 0px',
      threshold: 0.01,
    });

    lazyElements.forEach((element) => {
      lazyObserver.observe(element);
    });
  }
}
