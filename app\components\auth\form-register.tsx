"use client";
import { useState } from "react";
import { signUp } from "@/lib/auth/client";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Eye, EyeOff, User, Mail, Phone, Lock, AlertCircle, CheckCircle } from "lucide-react";
import { RegisterSchema, type RegisterInput } from "@/lib/validations/user/schema";
import { z } from "zod";
import { cn } from "@/lib/utils";

// Regex untuk validasi nomor WhatsApp Indonesia
const whatsappRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;

// Menambahkan CSS untuk menyembunyikan ikon mata bawaan browser
const passwordInputStyle = `
  /* Chrome, Safari, Edge */
  ::-ms-reveal,
  ::-ms-clear {
    display: none;
  }

  /* Firefox */
  input[type="password"]::-webkit-contacts-auto-fill-button,
  input[type="password"]::-webkit-credentials-auto-fill-button {
    visibility: hidden;
    display: none !important;
    pointer-events: none;
    height: 0;
    width: 0;
    margin: 0;
  }
`;

// Di bagian atas file, tambahkan interface untuk errors
interface FormErrors {
  name?: string[];
  email?: string[];
  phone?: string[];
  password?: string[];
  confirmPassword?: string[];
  _form?: string[];
  [key: string]: string[] | undefined;
}

export default function FromRegister() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState("");
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: ""
  });

  // State untuk validasi password
  const [passwordValidation, setPasswordValidation] = useState({
    hasLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false
  });

  // Validasi dengan Zod
  const validateField = (name: string, value: string) => {
    const newErrors: FormErrors = {};

    // Buat partial data untuk validasi field spesifik
    const partialData: Partial<RegisterInput> = {
      ...formData,
      [name]: value
    };

    try {
      // Validasi field berdasarkan namanya
      switch (name) {
        case 'name':
          // Validasi nama secara langsung
          z.string()
            .min(1, { message: "Nama harus diisi" })
            .min(3, { message: "Nama minimal 3 karakter" })
            .parse(value);
          break;
        case 'email':
          // Validasi email secara langsung
          z.string()
            .min(1, { message: "Email harus diisi" })
            .email({ message: "Format email tidak valid" })
            .parse(value);
          break;
        case 'phone':
          // Validasi nomor WhatsApp secara langsung
          z.string()
            .min(1, { message: "Nomor WhatsApp harus diisi" })
            .regex(whatsappRegex, {
              message: "Format nomor WhatsApp tidak valid (contoh: 08XXXXXXXXXX)"
            })
            .parse(value);
          break;
        case 'password':
          // Validasi password secara langsung
          z.string()
            .min(1, { message: "Password harus diisi" })
            .min(8, { message: "Password minimal 8 karakter" })
            .regex(/[A-Z]/, { message: "Password harus mengandung huruf besar" })
            .regex(/[a-z]/, { message: "Password harus mengandung huruf kecil" })
            .regex(/[0-9]/, { message: "Password harus mengandung angka" })
            .parse(value);
          break;
        case 'confirmPassword':
          // Untuk confirmPassword, validasi keduanya
          if (partialData.password !== value) {
            newErrors.confirmPassword = ["Password tidak sama"];
          }
          break;
      }
    } catch (error: unknown) {
      const zodError = error as { errors?: Array<{ path: string[], message: string }> };
      if (zodError.errors && zodError.errors.length > 0) {
        zodError.errors
          .filter(err => err.path[0] === name)
          .forEach(err => {
            if (!newErrors[name as keyof FormErrors]) {
              newErrors[name as keyof FormErrors] = [];
            }
            newErrors[name as keyof FormErrors]?.push(err.message);
          });
      } else if (error instanceof z.ZodError) {
        // Menangani error Zod secara langsung
        if (!newErrors[name as keyof FormErrors]) {
          newErrors[name as keyof FormErrors] = [];
        }
        error.errors.forEach(err => {
          newErrors[name as keyof FormErrors]?.push(err.message);
        });
      }
    }

    return newErrors;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validasi password secara langsung
    if (name === 'password') {
      setPasswordValidation({
        hasLength: value.length >= 8,
        hasUppercase: /[A-Z]/.test(value),
        hasLowercase: /[a-z]/.test(value),
        hasNumber: /[0-9]/.test(value)
      });
    }

    // Validasi saat input dengan Zod
    const fieldErrors = validateField(name, value);
    if (Object.keys(fieldErrors).length > 0) {
      setErrors(prev => ({ ...prev, [name as keyof FormErrors]: fieldErrors[name as keyof FormErrors] }));
    } else {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name as keyof FormErrors];
        return newErrors;
      });
    }
  };

  // Tambahkan handler untuk validasi saat field kehilangan fokus
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Validasi saat input kehilangan fokus
    const fieldErrors = validateField(name, value);
    if (Object.keys(fieldErrors).length > 0) {
      setErrors(prev => ({ ...prev, [name as keyof FormErrors]: fieldErrors[name as keyof FormErrors] }));
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Reset errors and success message
    setErrors({});
    setSuccess("");

    // Validasi semua field sebelum submit
    let hasErrors = false;
    const newErrors: FormErrors = {};

    // Validate each field and collect errors
    Object.entries(formData).forEach(([fieldName, value]) => {
      const validationErrors = validateField(fieldName as keyof typeof formData, value as string);
      if (validationErrors && Array.isArray(validationErrors) && validationErrors.length > 0) {
        newErrors[fieldName] = validationErrors;
        hasErrors = true;
      }
    });

    // If we have validation errors, update state and stop submission
    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Submit registration data
    try {
      setLoading(true);

      // Show processing message for better UX
      setSuccess("Memproses pendaftaran...");

      console.log('🔍 Attempting registration with Better Auth...');

      // Use Better Auth signUp
      const result = await signUp.email({
        email: formData.email,
        password: formData.password,
        name: formData.name,
        // Don't include phone here as it's not in the Better Auth signUp API
      });

      // Update user profile with phone after registration if needed
      if (!result.error) {
        // Handle phone number separately if needed
        console.log("Phone number will be handled separately:", formData.phone);
      }

      console.log('Registration response:', result);

      if (result.error) {
        setSuccess(""); // Clear success message
        setErrors({ _form: [result.error.message || "Terjadi kesalahan saat mendaftar. Silakan coba lagi."] });
      } else {
        console.log("✅ Registration success!");
        // Show success message before redirect
        setSuccess("Pendaftaran berhasil! Mengalihkan ke halaman login...");

        // Delay redirect to show success message
        setTimeout(() => {
          // Redirect to login page
          router.push('/login?registered=true');
        }, 1500);
      }
    } catch (error) {
      console.error("Error during registration:", error);
      setSuccess(""); // Clear success message
      setErrors({ _form: ["Terjadi kesalahan saat mendaftar. Silakan coba lagi."] });
    } finally {
      if (!success) {
        setLoading(false);
      }
    }
  };

  // Cek apakah form valid
  const isFormValid = () => {
    try {
      RegisterSchema.parse(formData);
      return Object.keys(errors).length === 0;
    } catch {
      return false;
    }
  };

  return (
    <>
      <div className="text-center mb-8">
        <div className="flex justify-center">
          <div className="h-14 w-14 rounded-full bg-blue-500 flex items-center justify-center text-white mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </div>
        <h2 className="text-2xl font-bold text-white mb-1">Daftar Akun Baru</h2>
        <p className="text-gray-400 text-sm">Silakan lengkapi data diri Anda</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Menambahkan style untuk sembunyikan ikon mata bawaan browser */}
        <style jsx global>{passwordInputStyle}</style>

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center gap-1.5">
            <User className="h-4 w-4 text-violet-500 dark:text-violet-400" />
            <span>Nama Lengkap</span>
          </label>
          <div className="relative">
            <input
              type="text"
              name="name"
              id="name"
              value={formData.name}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Masukkan nama lengkap"
              className={cn(
                "block w-full px-4 py-3 border rounded-lg bg-transparent",
                "text-gray-900 dark:text-gray-100 placeholder-gray-500",
                "focus:outline-none focus:ring-2 focus:ring-violet-500/30 dark:focus:ring-violet-400/20",
                "transition-all duration-200 shadow-sm",
                errors.name
                  ? "border-red-300 dark:border-red-700/70 bg-red-50/50 dark:bg-red-900/10"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              )}
            />
            {errors.name && (
              <div className="absolute right-2 top-3 text-red-500 dark:text-red-400">
                <AlertCircle className="h-5 w-5" />
              </div>
            )}
          </div>
          {errors.name?.map((error, i) => (
            <p key={i} className="mt-1.5 text-sm text-red-500 dark:text-red-400 flex items-center gap-1">
              <span className="h-1 w-1 rounded-full bg-red-500 dark:bg-red-400"></span>
              {error}
            </p>
          ))}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center gap-1.5">
            <Mail className="h-4 w-4 text-violet-500 dark:text-violet-400" />
            <span>Email</span>
          </label>
          <div className="relative">
            <input
              type="email"
              name="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="<EMAIL>"
              className={cn(
                "block w-full px-4 py-3 border rounded-lg bg-transparent",
                "text-gray-900 dark:text-gray-100 placeholder-gray-500",
                "focus:outline-none focus:ring-2 focus:ring-violet-500/30 dark:focus:ring-violet-400/20",
                "transition-all duration-200 shadow-sm",
                errors.email
                  ? "border-red-300 dark:border-red-700/70 bg-red-50/50 dark:bg-red-900/10"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              )}
            />
            {errors.email && (
              <div className="absolute right-2 top-3 text-red-500 dark:text-red-400">
                <AlertCircle className="h-5 w-5" />
              </div>
            )}
          </div>
          {errors.email?.map((error, i) => (
            <p key={i} className="mt-1.5 text-sm text-red-500 dark:text-red-400 flex items-center gap-1">
              <span className="h-1 w-1 rounded-full bg-red-500 dark:bg-red-400"></span>
              {error}
            </p>
          ))}
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center gap-1.5">
            <Phone className="h-4 w-4 text-violet-500 dark:text-violet-400" />
            <span>Nomor WhatsApp</span>
          </label>
          <div className="relative">
            <input
              type="tel"
              name="phone"
              id="phone"
              value={formData.phone}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="08xxxxxxxxxx (WhatsApp)"
              className={cn(
                "block w-full px-4 py-3 border rounded-lg bg-transparent",
                "text-gray-900 dark:text-gray-100 placeholder-gray-500",
                "focus:outline-none focus:ring-2 focus:ring-violet-500/30 dark:focus:ring-violet-400/20",
                "transition-all duration-200 shadow-sm",
                errors.phone
                  ? "border-red-300 dark:border-red-700/70 bg-red-50/50 dark:bg-red-900/10"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              )}
            />
            {errors.phone && (
              <div className="absolute right-2 top-3 text-red-500 dark:text-red-400">
                <AlertCircle className="h-5 w-5" />
              </div>
            )}
          </div>
          {errors.phone?.map((error, i) => (
            <p key={i} className="mt-1.5 text-sm text-red-500 dark:text-red-400 flex items-center gap-1">
              <span className="h-1 w-1 rounded-full bg-red-500 dark:bg-red-400"></span>
              {error}
            </p>
          ))}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center gap-1.5">
            <Lock className="h-4 w-4 text-violet-500 dark:text-violet-400" />
            <span>Password</span>
          </label>
          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              name="password"
              id="password"
              value={formData.password}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Minimal 8 karakter"
              autoComplete="off"
              autoCorrect="off"
              spellCheck="false"
              data-lpignore="true"
              data-form-type="password"
              className={cn(
                "block w-full px-4 py-3 border rounded-lg bg-transparent",
                "text-gray-900 dark:text-gray-100 placeholder-gray-500",
                "focus:outline-none focus:ring-2 focus:ring-violet-500/30 dark:focus:ring-violet-400/20",
                "transition-all duration-200 shadow-sm pr-10",
                errors.password
                  ? "border-red-300 dark:border-red-700/70 bg-red-50/50 dark:bg-red-900/10"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              )}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-violet-600 dark:text-gray-500 dark:hover:text-violet-400 transition-colors"
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>

          {/* Password validation checklist */}
          <div className="mt-2.5 space-y-1.5 px-1">
            <div className="flex items-center gap-1.5">
              <div className={`h-4 w-4 rounded-full flex items-center justify-center ${passwordValidation.hasLength ? 'bg-violet-100 dark:bg-violet-900/30' : 'bg-gray-100 dark:bg-gray-800'}`}>
                {passwordValidation.hasLength ? (
                  <CheckCircle className="h-3 w-3 text-violet-600 dark:text-violet-400" />
                ) : (
                  <div className="h-1.5 w-1.5 rounded-full bg-gray-400 dark:bg-gray-600"></div>
                )}
              </div>
              <p className={`text-xs ${passwordValidation.hasLength ? 'text-violet-700 dark:text-violet-400 font-medium' : 'text-gray-600 dark:text-gray-400'}`}>
                Minimal 8 karakter
              </p>
            </div>

            <div className="flex items-center gap-1.5">
              <div className={`h-4 w-4 rounded-full flex items-center justify-center ${passwordValidation.hasUppercase ? 'bg-violet-100 dark:bg-violet-900/30' : 'bg-gray-100 dark:bg-gray-800'}`}>
                {passwordValidation.hasUppercase ? (
                  <CheckCircle className="h-3 w-3 text-violet-600 dark:text-violet-400" />
                ) : (
                  <div className="h-1.5 w-1.5 rounded-full bg-gray-400 dark:bg-gray-600"></div>
                )}
              </div>
              <p className={`text-xs ${passwordValidation.hasUppercase ? 'text-violet-700 dark:text-violet-400 font-medium' : 'text-gray-600 dark:text-gray-400'}`}>
                Mengandung huruf besar (A-Z)
              </p>
            </div>

            <div className="flex items-center gap-1.5">
              <div className={`h-4 w-4 rounded-full flex items-center justify-center ${passwordValidation.hasLowercase ? 'bg-violet-100 dark:bg-violet-900/30' : 'bg-gray-100 dark:bg-gray-800'}`}>
                {passwordValidation.hasLowercase ? (
                  <CheckCircle className="h-3 w-3 text-violet-600 dark:text-violet-400" />
                ) : (
                  <div className="h-1.5 w-1.5 rounded-full bg-gray-400 dark:bg-gray-600"></div>
                )}
              </div>
              <p className={`text-xs ${passwordValidation.hasLowercase ? 'text-violet-700 dark:text-violet-400 font-medium' : 'text-gray-600 dark:text-gray-400'}`}>
                Mengandung huruf kecil (a-z)
              </p>
            </div>

            <div className="flex items-center gap-1.5">
              <div className={`h-4 w-4 rounded-full flex items-center justify-center ${passwordValidation.hasNumber ? 'bg-violet-100 dark:bg-violet-900/30' : 'bg-gray-100 dark:bg-gray-800'}`}>
                {passwordValidation.hasNumber ? (
                  <CheckCircle className="h-3 w-3 text-violet-600 dark:text-violet-400" />
                ) : (
                  <div className="h-1.5 w-1.5 rounded-full bg-gray-400 dark:bg-gray-600"></div>
                )}
              </div>
              <p className={`text-xs ${passwordValidation.hasNumber ? 'text-violet-700 dark:text-violet-400 font-medium' : 'text-gray-600 dark:text-gray-400'}`}>
                Mengandung angka (0-9)
              </p>
            </div>
          </div>

          {errors.password?.map((error, i) => (
            <p key={i} className="mt-1.5 text-sm text-violet-500 dark:text-violet-400 flex items-center gap-1">
              <span className="h-1 w-1 rounded-full bg-violet-500 dark:bg-violet-400"></span>
              {error}
            </p>
          ))}
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center gap-1.5">
            <Lock className="h-4 w-4 text-violet-500 dark:text-violet-400" />
            <span>Konfirmasi Password</span>
          </label>
          <div className="relative">
            <input
              type={showConfirmPassword ? "text" : "password"}
              name="confirmPassword"
              id="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Konfirmasi password"
              autoComplete="off"
              autoCorrect="off"
              spellCheck="false"
              data-lpignore="true"
              data-form-type="password"
              className={cn(
                "block w-full px-4 py-3 border rounded-lg bg-transparent",
                "text-gray-900 dark:text-gray-100 placeholder-gray-500",
                "focus:outline-none focus:ring-2 focus:ring-violet-500/30 dark:focus:ring-violet-400/20",
                "transition-all duration-200 shadow-sm pr-10",
                errors.confirmPassword
                  ? "border-red-300 dark:border-red-700/70 bg-red-50/50 dark:bg-red-900/10"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              )}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-violet-600 dark:text-gray-500 dark:hover:text-violet-400 transition-colors"
            >
              {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
          {errors.confirmPassword?.map((error, i) => (
            <p key={i} className="mt-1.5 text-sm text-violet-500 dark:text-violet-400 flex items-center gap-1">
              <span className="h-1 w-1 rounded-full bg-violet-500 dark:bg-violet-400"></span>
              {error}
            </p>
          ))}
        </div>

        {/* Form errors */}
        {errors._form && (
          <div className="p-3 bg-violet-50 dark:bg-violet-900/20 border border-violet-200 dark:border-violet-800 rounded-lg mt-4">
            {errors._form.map((error, i) => (
              <p key={i} className="text-sm text-violet-600 dark:text-violet-400 flex items-center gap-1">
                <AlertCircle className="h-4 w-4 shrink-0" />
                <span>{error}</span>
              </p>
            ))}
          </div>
        )}

        {/* Submit button */}
        <button
          type="submit"
          disabled={loading || !isFormValid()}
          className="w-full rounded-lg bg-gradient-to-r from-violet-600 to-indigo-600 px-4 py-3 text-sm font-medium text-white shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200 transform hover:-translate-y-0.5"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Mendaftar...
            </div>
          ) : (
            "Daftar"
          )}
        </button>

        {/* Login link */}
        <p className="text-center text-sm text-gray-600 dark:text-gray-400">
          Sudah punya akun?{" "}
          <Link href="/login" className="text-violet-600 dark:text-violet-400 hover:text-indigo-600 dark:hover:text-indigo-300 font-medium transition-colors">
            Masuk sekarang
          </Link>
        </p>
      </form>
    </>
  );
}
