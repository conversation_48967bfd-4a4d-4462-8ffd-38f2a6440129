import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { SessionProvider } from "@/app/components/providers/session-provider";
import { ThemeProvider } from "@/app/components/providers/theme-provider";
import ToasterProvider from "@/app/components/providers/toaster-provider";
import { getSession } from "@/lib/auth/server";
import { MidtransProvider } from "@/app/components/providers/midtrans-provider";
import { PaymentAnimationProvider } from "@/app/components/providers/payment-animation-provider";
import { RentalAnimationProvider } from "@/app/components/providers/rental-animation-provider";
import { PrefetchProvider } from "@/app/components/providers/prefetch-provider";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "RentalGenset | Solusi Penyewaan Genset Terpercaya",
  description: "Platform rental genset modern dengan layanan profesional untuk berbagai kebutuhan listrik industri dan komersial",
  keywords: ["rental genset", "sewa genset", "generator listrik", "penyewaan genset"],
  authors: [{ name: "RentalGenset Team" }],
  creator: "RentalGenset",
  publisher: "RentalGenset",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "RentalGenset",
  },
  openGraph: {
    type: "website",
    locale: "id_ID",
    url: "https://rental-genset.com",
    title: "RentalGenset | Solusi Penyewaan Genset Terpercaya",
    description: "Platform rental genset modern dengan layanan profesional untuk berbagai kebutuhan listrik industri dan komersial",
    siteName: "RentalGenset",
  },
  twitter: {
    card: "summary_large_image",
    title: "RentalGenset | Solusi Penyewaan Genset Terpercaya",
    description: "Platform rental genset modern dengan layanan profesional untuk berbagai kebutuhan listrik industri dan komersial",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" },
  ],
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Menggunakan try-catch untuk mencegah error dari getSession() mematikan aplikasi
  let session = null;
  try {
    session = await getSession();
  } catch (error) {
    console.error("Error in getSession() call:", error);
    // Tidak perlu throw error, biarkan aplikasi tetap berjalan
  }

  // Script untuk menangani tema
  const themeScript = `
    (function() {
      // Coba mengambil tema dari localStorage
      function getTheme() {
        try {
          return localStorage.getItem('rental-genset-theme') || 'light';
        } catch (e) {
          return 'light';
        }
      }

      // Fungsi untuk menerapkan tema
      function applyTheme(theme) {
        const d = document.documentElement;

        // Hapus kelas tema lama
        d.classList.remove('light', 'dark');

        // Tambahkan kelas untuk tema baru
        d.classList.add(theme);

        // Atur atribut data-theme
        d.setAttribute('data-theme', theme);
      }

      // Terapkan tema yang tersimpan
      var savedTheme = getTheme();
      applyTheme(savedTheme);

      // Tambahkan listener untuk perubahan tema
      document.addEventListener('themeChange', function(e) {
        if (e.detail && e.detail.theme) {
          applyTheme(e.detail.theme);
        }
      });
    })();
  `;

  // Script untuk menangani error fetch dari Better Auth
  const errorHandlingScript = `
    (function() {
      // Error handling untuk fetch
      window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.toString().includes('Failed to fetch')) {
          console.warn('Unhandled fetch error detected:', event.reason);

          // Mencegah error mempengaruhi UI
          event.preventDefault();
          event.stopPropagation();
        }
      });
    })();
  `;

  // Performance optimization script
  const performanceScript = `
    (function() {
      // Initialize bundle optimization
      if (typeof window !== 'undefined') {
        // Preload critical modules
        const criticalModules = ['react-hook-form', 'date-fns', 'framer-motion'];

        // Setup intersection observer for lazy loading
        const lazyObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const element = entry.target;
              const moduleName = element.dataset.lazy;

              if (moduleName) {
                import(moduleName).then(() => {
                  element.removeAttribute('data-lazy');
                  lazyObserver.unobserve(element);
                }).catch(() => {});
              }
            }
          });
        }, { rootMargin: '100px 0px', threshold: 0.01 });

        // Observe lazy elements
        document.addEventListener('DOMContentLoaded', () => {
          const lazyElements = document.querySelectorAll('[data-lazy]');
          lazyElements.forEach((element) => lazyObserver.observe(element));
        });
      }
    })();
  `;

  return (
    <html lang="id" suppressHydrationWarning className={inter.variable}>
      <head>
        {/* Critical resource hints */}
        {/* <ResourceHints /> */}

        {/* Script untuk menerapkan tema sebelum hydration untuk menghindari flicker */}
        <script dangerouslySetInnerHTML={{ __html: themeScript }} />
        {/* Script untuk menangani error fetch */}
        <script dangerouslySetInnerHTML={{ __html: errorHandlingScript }} />
        {/* Performance optimization script */}
        <script dangerouslySetInnerHTML={{ __html: performanceScript }} />

        {/* Performance optimizations */}
        <meta name="format-detection" content="telephone=no, date=no, email=no, address=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />

        {/* PWA meta tags */}
        <meta name="application-name" content="RentalGenset" />
        <meta name="apple-mobile-web-app-title" content="RentalGenset" />
        <meta name="msapplication-TileColor" content="#8b5cf6" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* Preload critical CSS */}
        <link rel="preload" href="/globals.css" as="style" />
      </head>
      <body
        className={`${inter.className} font-sans antialiased`}
        suppressHydrationWarning
      >
        <PrefetchProvider>
          <SessionProvider session={session}>
            <ThemeProvider
              attribute="data-theme"
              defaultTheme="light"
              enableSystem
              storageKey="rental-genset-theme"
            >
              <PaymentAnimationProvider>
                <RentalAnimationProvider>
                  {/* Modern responsive container with improved background */}
                  <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800 transition-colors duration-300">
                    {/* Main content wrapper with better responsive behavior */}
                    <div className="relative min-h-screen">
                      {children}
                    </div>
                  </div>
                </RentalAnimationProvider>
              </PaymentAnimationProvider>

              {/* Global providers */}
              <ToasterProvider />
              <MidtransProvider />
            </ThemeProvider>
          </SessionProvider>
        </PrefetchProvider>
      </body >
    </html >
  );
}
