import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { WhatsAppService } from "@/lib/services/whatsapp";

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { rentalId, status } = await request.json();

    if (!rentalId || !status) {
      return NextResponse.json({ error: "Missing rentalId or status" }, { status: 400 });
    }

    console.log("[TEST_WEBHOOK] Updating payment status:", { rentalId, status });

    // Update payment status with rental and user data
    const payment = await prisma.payment.update({
      where: { rentalId },
      data: {
        status,
        transactionId: `TEST_${Date.now()}`
      },
      include: {
        rental: {
          include: {
            user: {
              select: {
                name: true,
                phone: true,
                email: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    // Update rental status if deposit is paid
    if (status === "DEPOSIT_PAID") {
      await prisma.rental.update({
        where: { id: rentalId },
        data: {
          status: "ACTIVE",

        }
      });

      // Send WhatsApp notification for deposit payment
      console.log(`[TEST_WEBHOOK] Sending WhatsApp notification for deposit payment: ${rentalId}`);

      try {
        await WhatsAppService.sendAdminDepositNotification(
          rentalId,
          payment.rental.user.name || 'Unknown Customer',
          payment.rental.user.phone || 'No phone',
          payment.rental.user.email || 'No email',
          payment.rental.product.name,
          payment.deposit || 0,
          new Date()
        );

        console.log(`✅ WhatsApp admin notification sent for deposit payment: ${rentalId}`);
      } catch (whatsappError) {
        console.error(`❌ Failed to send WhatsApp notification for deposit payment ${rentalId}:`, whatsappError);
      }
    }

    console.log("[TEST_WEBHOOK] Payment updated successfully:", payment.id);

    return NextResponse.json({
      success: true,
      payment,
      message: "Payment status updated successfully"
    });
  } catch (error) {
    console.error("[TEST_WEBHOOK] Error:", error);
    return NextResponse.json(
      { error: "Failed to update payment status" },
      { status: 500 }
    );
  }
}
