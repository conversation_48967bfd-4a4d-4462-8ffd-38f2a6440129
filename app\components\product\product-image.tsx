"use client";

import Image from "next/image";
import { LuImage } from "react-icons/lu";
import { useState } from "react";
import { ProductImage as WebPProductImage } from "@/app/components/ui/webp-image";

interface ProductImageProps {
    imageUrl?: string | null;
    image?: string | null;
    name?: string;
    size?: "sm" | "md" | "lg" | "full";
}

const sizeClasses = {
    sm: "w-24 h-24",
    md: "w-32 h-32",
    lg: "w-48 h-48",
    full: "w-full h-full"
};

export function ProductImage({ imageUrl, image, name = "Product", size = "md" }: ProductImageProps) {
    const sizeClass = sizeClasses[size];
    const [imageError, setImageError] = useState(false);

    const imgSrc = imageUrl || image || "";

    if ((imageUrl || image) && !imageError) {
        return (
            <div className={`relative ${sizeClass} bg-gray-100 overflow-hidden`} style={{ minHeight: size === "full" ? "100%" : "auto" }}>
                <WebPProductImage
                    src={imgSrc}
                    alt={name}
                    fill
                    priority
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    onError={() => {
                        console.log("Error loading image:", imgSrc);
                        setImageError(true);
                    }}
                />
            </div>
        );
    }

    return (
        <div className={`${sizeClass} bg-gray-100 flex items-center justify-center`}>
            <LuImage className="w-8 h-8 text-gray-400" />
        </div>
    );
} 
