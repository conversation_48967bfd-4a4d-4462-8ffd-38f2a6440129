'use client';

import { useEffect, useState } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * ClientOnly component to prevent hydration mismatch
 * Renders fallback on server and children on client
 */
export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * NoSSR component - alias for ClientOnly for better semantics
 */
export const NoSSR = ClientOnly;

/**
 * Hook to check if component has mounted (client-side)
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely use window object
 */
export function useWindow() {
  const [windowObj, setWindowObj] = useState<Window | null>(null);

  useEffect(() => {
    setWindowObj(window);
  }, []);

  return windowObj;
}

/**
 * Component for dynamic content that might cause hydration mismatch
 */
interface DynamicContentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

export function DynamicContent({ children, fallback, className }: DynamicContentProps) {
  return (
    <div className={className} suppressHydrationWarning>
      <ClientOnly fallback={fallback}>
        {children}
      </ClientOnly>
    </div>
  );
}

/**
 * Safe Image component that prevents hydration mismatch
 */
interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallback?: React.ReactNode;
}

export function SafeImage({ src, alt, width, height, className, fallback }: SafeImageProps) {
  const defaultFallback = (
    <div 
      className={`bg-gray-200 dark:bg-gray-700 flex items-center justify-center ${className}`}
      style={{ width, height }}
    >
      <span className="text-gray-500 dark:text-gray-400 text-sm">Loading...</span>
    </div>
  );

  return (
    <ClientOnly fallback={fallback || defaultFallback}>
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        loading="lazy"
      />
    </ClientOnly>
  );
}
